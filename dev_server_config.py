# Ignore_for_mass_testing
#!/usr/bin/env python3
"""
Development Server Configuration for Slicer Application
======================================================

This file contains configuration settings for the development server
to better simulate production environment behavior.
"""

# Default configuration for development server
DEV_SERVER_CONFIG = {
    # Server settings
    'host': 'localhost',
    'port': 8000,
    
    # Production simulation settings
    'production_path': '/var/www/html/',
    'simulate_https': False,
    'server_name': 'localhost',
    'debug_mode': True,
    'mock_filesystem': True,
    
    # Apache/mod_wsgi simulation
    'server_software': 'Apache/2.4.6 (CentOS) mod_wsgi/4.6.4 Python/3.6',
    'server_admin': 'webmaster@localhost',
    
    # WSGI modules to load (you can customize this list)
    'wsgi_modules': [
        'slicer_wsgi_index',
        'slicer_wsgi_login',
        'slicer_wsgi_dashboard',
        'slicer_wsgi_reports',
        'slicer_wsgi_loader',
        'slicer_wsgi_datastore',
        'slicer_wsgi_debug',
        'slicer_wsgi_management',
        'slicer_wsgi_organization',
        'slicer_wsgi_profiles',
        'slicer_wsgi_sites',
        'slicer_wsgi_timezones',
        'slicer_wsgi_htmlfiles',
        'slicer_wsgi_deviceupload',
        'slicer_wsgi_ldapsupport',
        'slicer_wsgi_videos',
        'slicer_wsgi_certificates',
        'slicer_wsgi_permissions',
        'slicer_wsgi_investigate',
        'slicer_wsgi_jamf',
        'slicer_wsgi_address2location',
        'slicer_wsgi_thirdparty',
        'slicer_wsgi_multimedia',
        'slicer_wsgi_dataport',
        'slicer_wsgi_speedtest',
    ]
}

# Production-like configuration
PRODUCTION_SIMULATION_CONFIG = {
    **DEV_SERVER_CONFIG,
    'debug_mode': False,
    'mock_filesystem': False,
    'simulate_https': True,
    'server_name': 'slicer.cardinalhealth.net',
}

# Configuration presets
PRESETS = {
    'development': DEV_SERVER_CONFIG,
    'production_sim': PRODUCTION_SIMULATION_CONFIG,
    'local_https': {
        **DEV_SERVER_CONFIG,
        'simulate_https': True,
        'port': 8443,
    }
}

def get_config(preset='development'):
    """Get configuration for a specific preset"""
    return PRESETS.get(preset, DEV_SERVER_CONFIG).copy()

def merge_config(base_config, overrides):
    """Merge configuration overrides with base configuration"""
    config = base_config.copy()
    config.update(overrides)
    return config
