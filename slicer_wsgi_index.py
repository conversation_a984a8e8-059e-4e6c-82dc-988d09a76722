# A index for slicer page services

service = "index"
version = service + '.0.5'

_ = """
This file gets loaded to:
/var/www/html/index.py

using:
sudo vi /var/www/html/index.py

or for development gets loaded to:
sudo vi /var/www/html/indexd.py


It also requires:

sudo vi /etc/httpd/conf.d/python-index.conf
----- start copy -----
WSGIScriptAlias /index /var/www/html/index.py
----- end copy -----

It also requires:

sudo vi /etc/httpd/conf/httpd.conf
----- start copy / paste to end of file -----
<Directory "/">
DirectoryIndex index
</Directory>
----- end copy -----

sudo chown apache:apache /var/www/html/index.py

sudo systemctl restart httpd

sudo systemctl status httpd

Slicer03:
sudo cat /var/log/httpd/error_log
sudo cat /var/log/httpd/access_log
sudo cat /var/log/httpd/ssl_access_log
sudo cat /var/log/httpd/ssl_error_log
sudo cat /var/log/httpd/ssl_request_log

test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import index; print(index.make_body({'REQUEST_METHOD':'GET', 'QUERY_STRING':'','REMOTE_ADDR':'0.0.0.0'}))"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net


"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest slicer_wsgi_index

"""

import copy
import traceback
import json
import os
import shlex
import subprocess
import sys
import time
import unittest

startup_exceptions = ''
s_loader_module_save_path = '/dev/shm/loader_modules.txt'

service_config = {}
path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)

try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)
except:
    pass

try:
    # python 2 and some early python 3
    from cgi import parse_qs
except:
    pass

try:
    # later python 3
    from urllib.parse import parse_qs
except:
    pass

try:
    import datastore
except:
    pass
try:
    import login
except:
    pass
try:
    import permissions
except:
    pass
try:
    import rings
except:
    pass
try:
    import scan
except:
    pass
try:
    import tasks
except:
    pass
try:
    import upload
except:
    pass
try:
    import htmlfiles
except:
    pass
try:
    import certificates
except:
    pass
try:
    import watchdog
except:
    pass
try:
    import dashboard
except:
    pass
try:
    import devicecommand
except:
    pass

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass

# ====================================
def make_not_allow_list_from_datastore(data_store_content):
    # ====================================
    list_of_services_to_not_allow = []
    for key in data_store_content.keys():
        if 'service_loader_' in key:
            the_service = key.replace('service_loader_','').split('_')[0]
            the_key = 'service_loader_' + the_service + '_allow_runner'
            if the_key in data_store_content:
                the_value = data_store_content[the_key]
                if the_value == 'no':
                    list_of_services_to_not_allow.append(the_service)
    return list_of_services_to_not_allow

# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ----------------------------
def do_one_command(command):
    # ----------------------------
    command_splits = shlex.split(command)
    # command_splits = command.split(" ")
    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()
    return (mem_string, fails)


# ====================================
def disk_report():
    # ====================================
    report = ""
    pass_string, fails = do_one_command('df -k')

    for line in pass_string.split('\n'):
        if not 'tmpfs' in line:
            splits = line.split()
            try:
                report += splits[0] + ' ' + splits[4] + '\n'
            except:
                pass

    pass_string, fails = do_one_command('df -i')

    for line in pass_string.split('\n'):
        if not 'tmpfs' in line:
            splits = line.split()
            try:
                report += splits[0] + ' ' + splits[4] + '\n'
            except:
                pass

    return report


# ====================================
def make_body(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    if environ['REQUEST_METHOD'] == 'GET':
        return make_body_GET(environ)

    body = ''

    try:
        if permissions.permission_prefix_allowed(environ, 'development_'):
            try:
                if environ['REQUEST_METHOD'] == 'POST':
                    return make_body_POST(environ)
            except:
                body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
        else:
            body = ""
            body += "<br><br><br><br><br>"
            body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"
    except:
        body += '<B>Exception on permissions call!</B>'

    return body, other


# ====================================
def make_body_POST(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    try:
        request_body_size = int(environ.get('CONTENT_LENGTH', 0))
    except (ValueError):
        request_body_size = 0
    request_body = environ['wsgi.input'].read(request_body_size)

    d = parse_qs(request_body.decode('utf-8'))

    if 'process_start_stop' in d:
        the_process = d['process_start_stop'][0]

        if 'process_start_stop_value' in d:
            the_value = d['process_start_stop_value'][0]

            the_request_file = '/dev/shm/' + 'service_start_stop_request_' + str(time.time()) + '.txt'

            try:
                open(the_request_file, 'w').write(the_process + ' ' + the_value)
            except:
                pass

            # hang out here a little bit, until it is processed, but bail after that
            try_to_wait = True
            time_start = time.time()
            while try_to_wait:
                if not os.path.isfile(the_request_file):
                    try_to_wait = False
                else:
                    if time.time() - time_start > 1.0:
                        try_to_wait = False
                    else:
                        time.sleep(0.1)

    #            return ('see: ' + the_process + ' ' + the_value + ' -> ' + the_request_file)

    # then return what GET would have done
    return make_body_GET(environ)

# ====================================
def make_body_GET_content(module_permissions, environ={}):
    # ====================================
    to_show_above_login = module_permissions['to_show_above_login']
    to_show_for_login = module_permissions['to_show_for_login']
    to_show_below_login = module_permissions['to_show_below_login']
    current_user = module_permissions['current_user']

    site_title = 'Slicer 2.0'
    if 'site_title' in service_config:
        site_title = service_config['site_title']

    site_build = ''
    if 'build' in service_config:
        site_build = service_config['build']

    body = ''
    try:
        # Modern Minimalist Enterprise Admin Interface
        # Inline CSS and JavaScript for production compatibility
        body += '''
<style type="text/css">
/* Modern Enterprise Admin Styles */
:root {
    --primary-color: #1a73e8;
    --secondary-color: #5f6368;
    --success-color: #137333;
    --warning-color: #ea4335;
    --surface-color: #ffffff;
    --background-color: #f8f9fa;
    --border-color: #dadce0;
    --text-primary: #202124;
    --text-secondary: #5f6368;
    --border-radius: 8px;
    --shadow-sm: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);
    --shadow-md: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 2px 6px 2px rgba(60, 64, 67, 0.15);
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Google Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.5;
    font-size: 14px;
    min-height: 100vh;
}

.admin-layout {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.admin-header {
    background: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    padding: 0 24px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-sm);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 24px;
}

.logo {
    font-size: 20px;
    font-weight: 500;
    color: var(--text-primary);
    text-decoration: none;
}

.build-badge {
    background: #e8f0fe;
    color: var(--primary-color);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    border-radius: 8px;
    background: #f8f9fa;
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
    cursor: pointer;
    text-decoration: none;
    color: var(--text-primary);
}

.user-menu:hover {
    background: #e8eaed;
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(26, 115, 232, 0.1);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 13px;
    text-transform: uppercase;
}

.user-info-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    line-height: 1.2;
}

.user-name {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 14px;
    margin: 0;
}

.user-role {
    color: var(--text-secondary);
    font-size: 11px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 0;
}

.logout-icon {
    margin-left: 8px;
    opacity: 0.6;
    font-size: 16px;
    transition: opacity 0.2s ease;
}

.user-menu:hover .logout-icon {
    opacity: 1;
}

.main-content {
    flex: 1;
    padding: 32px 24px;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

.content-section {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    margin-bottom: 24px;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.section-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
    background: #fafbfc;
}

.section-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary);
    margin: 0;
}

.section-subtitle {
    font-size: 13px;
    color: var(--text-secondary);
    margin-top: 4px;
}

.section-content {
    padding: 24px;
}

.module-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 20px;
    align-items: stretch;
}

.module-card {
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 24px 20px;
    text-decoration: none;
    color: var(--text-primary);
    transition: all 0.15s ease;
    position: relative;
    overflow: hidden;
    min-height: 80px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.module-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(26, 115, 232, 0.15);
    transform: translateY(-1px);
    background: #fafbfc;
}

.module-card.primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1557b0 100%);
    color: white;
    border-color: var(--primary-color);
}

.module-card.success {
    background: linear-gradient(135deg, var(--success-color) 0%, #0f5132 100%);
    color: white;
    border-color: var(--success-color);
}

.module-card.warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #c5221f 100%);
    color: white;
    border-color: var(--warning-color);
}

.module-title {
    font-size: 14px;
    font-weight: 500;
    line-height: 1.2;
    margin: 0;
}

.module-name {
    font-size: 14px;
    font-weight: 500;
    line-height: 1.2;
    margin: 0;
}

.status-banner {
    background: linear-gradient(90deg, #fef7cd 0%, #fef3cd 100%);
    border: 1px solid #f9cf58;
    border-left: 4px solid #f9ab00;
    border-radius: var(--border-radius);
    padding: 16px 20px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.status-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    flex-shrink: 0;
}

.status-icon.untrusted {
    background: var(--warning-color);
}

.status-icon.trusted {
    background: var(--success-color);
}

.status-content {
    flex: 1;
}

.status-title {
    font-weight: 500;
    color: #7c2d12;
    margin-bottom: 4px;
}

.status-message {
    font-size: 13px;
    color: #92400e;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    text-decoration: none;
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-top: 8px;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: #1557b0;
    border-color: #1557b0;
}

.btn-danger {
    background: var(--warning-color);
    color: white;
    border-color: var(--warning-color);
}

.btn-danger:hover {
    background: #c5221f;
    border-color: #c5221f;
}

.btn-warning {
    background: #f59e0b;
    color: white;
    border-color: #f59e0b;
}

.btn-warning:hover {
    background: #d97706;
    border-color: #d97706;
}

.error-section {
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-left: 4px solid var(--warning-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 24px;
}

.error-title {
    color: #991b1b;
    font-weight: 500;
    margin-bottom: 8px;
}

.error-content {
    color: #7f1d1d;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
    font-size: 12px;
    line-height: 1.5;
    background: rgba(239, 68, 68, 0.05);
    padding: 12px;
    border-radius: 4px;
    overflow-x: auto;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .module-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: 16px;
    }
}

@media (max-width: 768px) {
    .admin-header {
        padding: 0 16px;
        height: 56px;
    }

    .header-left {
        gap: 16px;
    }

    .logo {
        font-size: 18px;
    }

    .main-content {
        padding: 24px 16px;
    }

    .module-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 12px;
    }

    .module-card {
        padding: 20px 16px;
        min-height: 70px;
    }

    .user-info-container {
        display: none;
    }

    .user-menu {
        padding: 8px 12px;
        gap: 8px;
    }

    .logout-icon {
        margin-left: 4px;
    }
}

@media (max-width: 480px) {
    .build-badge {
        display: none;
    }

    .module-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .module-card {
        padding: 16px 12px;
        min-height: 60px;
    }

    .module-title, .module-name {
        font-size: 13px;
    }

    .main-content {
        padding: 16px 12px;
    }
}

/* Status Badge Styles */
.status-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 4px;
}

.status-success {
    background: #dcfce7;
    color: #166534;
}

.status-warning {
    background: #fef3c7;
    color: #92400e;
}

.status-error {
    background: #fee2e2;
    color: #991b1b;
}

/* Module Card Variants */
.module-card.admin-module {
    border-left: 3px solid var(--warning-color);
    background: var(--surface-color);
}

.module-card.admin-module:hover {
    border-left-color: #c5221f;
    background: #fef2f2;
}

.module-card.primary {
    border-left: 3px solid var(--primary-color);
    background: var(--surface-color);
}

.module-card.primary:hover {
    border-left-color: #1557b0;
    background: #eff6ff;
}

/* Alert Styles */
.alert {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px 20px;
    border-radius: var(--border-radius);
    margin-bottom: 24px;
    border-left: 4px solid;
}

.alert-warning {
    background: #fffbeb;
    border-left-color: #f59e0b;
    color: #92400e;
}

.alert-icon {
    font-size: 18px;
    line-height: 1;
    margin-top: 2px;
}

.alert-content {
    flex: 1;
}

.alert-content strong {
    display: block;
    font-weight: 600;
    margin-bottom: 4px;
}

.alert-content p {
    margin: 0 0 12px 0;
    font-size: 14px;
    line-height: 1.5;
}

/* Additional styles for proper cards layout */
.card {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    margin-bottom: 24px;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.card-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
    background: #fafbfc;
}

.card-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary);
    margin: 0;
}

.card-subtitle {
    font-size: 13px;
    color: var(--text-secondary);
    margin-top: 4px;
}

.card-content {
    padding: 24px;
}

.user-info {
    background: linear-gradient(135deg, #e3f2fd 0%, #f8f9fa 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px 24px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.username {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary);
}

.module-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    text-decoration: none;
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
}

.module-button.danger {
    background: var(--warning-color);
    color: white;
    border-color: var(--warning-color);
}

.module-button.danger:hover {
    background: #c5221f;
    border-color: #c5221f;
}
</style>

<script>
// Professional navigation functionality
function URLjump(jumpLocation, buttonElement = null) {
    if (buttonElement) {
        const originalText = buttonElement.innerHTML;
        buttonElement.innerHTML = 'Loading...';
        buttonElement.style.pointerEvents = 'none';
        buttonElement.style.opacity = '0.7';

        setTimeout(() => {
            buttonElement.innerHTML = originalText;
            buttonElement.style.pointerEvents = 'auto';
            buttonElement.style.opacity = '1';
        }, 5000);
    }

    location.href = jumpLocation;
}

document.addEventListener('DOMContentLoaded', function() {
    // Add keyboard shortcuts for power users
    document.addEventListener('keydown', function(e) {
        if (e.altKey) {
            switch(e.key) {
                case 'h': e.preventDefault(); URLjump('index'); break;
                case 'd': e.preventDefault(); URLjump('dashboard'); break;
                case 'r': e.preventDefault(); URLjump('reports'); break;
                case 'l': e.preventDefault(); URLjump('login'); break;
            }
        }
    });

    // Add focus management for accessibility
    const buttons = document.querySelectorAll('.module-button');
    buttons.forEach((button, index) => {
        button.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                button.click();
            }
        });
    });

    console.log('Slicer Professional Interface ready');
});
</script>

<div class="admin-layout">
    <header class="admin-header">
        <div class="header-left">
            <a href="index" class="logo">''' + site_title + '''</a>'''

        if site_build:
            body += '''<span class="build-badge">''' + site_build + '''</span>'''

        body += '''</div>
        <div class="header-right">'''

        # User menu in header
        if current_user:
            user_initial = current_user[0].upper() if current_user else 'U'
            # Determine user role based on permissions
            user_role = 'User'
            if 'user_has_admin' in module_permissions and module_permissions['user_has_admin']:
                user_role = 'Administrator'
            elif current_user in ['admin', 'dev']:
                user_role = 'Administrator'

            body += '''
            <div class="user-menu" onclick="document.getElementById('logout-link').click()" title="Click to logout">
                <div class="user-avatar">''' + user_initial + '''</div>
                <div class="user-info-container">
                    <div class="user-name">''' + current_user + '''</div>
                    <div class="user-role">''' + user_role + '''</div>
                </div>
                <div class="logout-icon">⏻</div>
            </div>
            <a id="logout-link" href="login?action=logout" style="display: none;" onclick="URLjump('login?action=logout', this); return false;"></a>'''
        else:
            body += '''
            <a href="login" class="btn btn-primary" onclick="URLjump('login', this); return false;">Sign In</a>'''

        body += '''
        </div>
    </header>

    <main class="main-content">'''

        # System status section
        try:
            # Development quick login feature - only in dev environment
            query_string = environ.get('QUERY_STRING', '')
            if 'dev_login=' in query_string:
                from urllib.parse import parse_qs
                query_params = parse_qs(query_string)
                if 'dev_login' in query_params:
                    dev_user = query_params['dev_login'][0]
                    body += '''
    <div class="alert alert-warning">
        <div class="alert-icon">🔧</div>
        <div class="alert-content">
            <strong>Development Login Mode</strong>
            <p>Simulating login as user: ''' + dev_user + '''</p>
            <a href="index" class="btn btn-primary">Exit Dev Mode</a>
        </div>
    </div>'''
                    # Override current_user for development testing
                    current_user = dev_user
                    if dev_user in ['admin', 'dev']:
                        to_show_below_login = [
                            'datastore', 'debug', 'profiles', 'sites',
                            'htmlfiles', 'deviceupload', 'certificates',
                            'investigate', 'multimedia', 'speedtest',
                            'loader', 'management', 'organization', 'permissions',
                            'ldapsupport', 'jamf', 'address2location', 'thirdparty', 'dataport'
                        ]
                    else:
                        to_show_below_login = [
                            'datastore', 'debug', 'profiles', 'sites',
                            'htmlfiles', 'deviceupload', 'certificates',
                            'investigate', 'multimedia', 'speedtest'
                        ]

            if not datastore.trust():
                body += '''
    <div class="alert alert-warning">
        <div class="alert-icon">!</div>
        <div class="alert-content">
            <strong>System Status Alert</strong>
            <p>Datastore is not trusted. Please establish trust to continue.</p>
            <button type="button" class="btn btn-warning" onclick="URLjump('datastore?action=begintrust', this); return false;">
                Establish Trust
            </button>
        </div>
    </div>'''
        except:
            pass

        try:
            # Helper function to get display names
            def get_module_display_name(module):
                display_names = {
                    'index': 'Home',
                    'dashboard': 'Dashboard',
                    'reports': 'Reports',
                    'login': 'Login',
                    'datastore': 'Data Store',
                    'debug': 'Debug',
                    'loader': 'Loader',
                    'management': 'Management',
                    'organization': 'Organization',
                    'profiles': 'Profiles',
                    'sites': 'Sites',
                    'timezones': 'Time Zones',
                    'htmlfiles': 'HTML Files',
                    'deviceupload': 'Device Upload',
                    'ldapsupport': 'LDAP Support',
                    'videos': 'Videos',
                    'certificates': 'Certificates',
                    'permissions': 'Permissions',
                    'investigate': 'Investigate',
                    'jamf': 'JAMF',
                    'address2location': 'Address to Location',
                    'thirdparty': 'Third Party',
                    'multimedia': 'Multimedia',
                    'dataport': 'Data Port',
                    'speedtest': 'Speed Test'
                }
                return display_names.get(module, module.replace('_', ' ').title())

            # Available modules section
            if to_show_above_login:
                # Filter out 'index' since we're already on the home page
                modules_to_show = [module for module in to_show_above_login if module != 'index']

                if modules_to_show:
                    body += '''
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Modules</h3>
        </div>
        <div class="card-content">
            <div class="module-grid">'''

                    for module in modules_to_show:
                        display_name = get_module_display_name(module)
                        body += '''
                <a href="''' + module + '''" class="module-card" onclick="URLjump('''' + module + '''', this); return false;" title="Access ''' + display_name + ''' module">
                    <div class="module-name">''' + display_name + '''</div>
                </a>'''

                    body += '''
            </div>
        </div>
    </div>'''

            if current_user:
                # User welcome section - more subtle since user info is in header
                body += '''
    <div class="user-info">
        <div class="username">Welcome back, ''' + current_user + '''</div>
        <a href="login?action=logout" class="module-button danger" onclick="URLjump('login?action=logout', this); return false;" title="Logout current user">
            <span>Sign Out</span>
        </a>
    </div>'''

                # User modules
                if to_show_below_login:
                    # Separate admin and regular modules
                    admin_modules = [m for m in to_show_below_login if m in ['loader', 'management', 'organization', 'permissions', 'debug']]
                    regular_modules = [m for m in to_show_below_login if m not in admin_modules]

                    if regular_modules:
                        body += '''
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">User Applications</h3>
            <p class="card-subtitle">Authenticated user modules</p>
        </div>
        <div class="card-content">
            <div class="module-grid">'''

                        for module in regular_modules:
                            display_name = get_module_display_name(module)
                            status_indicator = ''
                            if module == 'datastore':
                                try:
                                    if datastore.trust():
                                        status_indicator = '<div class="status-badge status-success">Trusted</div>'
                                    else:
                                        status_indicator = '<div class="status-badge status-warning">Not Trusted</div>'
                                except:
                                    pass

                            body += '''
                <a href="''' + module + '''" class="module-card" onclick="URLjump('''' + module + '''', this); return false;" title="Access ''' + display_name + ''' module">
                    <div class="module-name">''' + display_name + '''</div>
                    ''' + status_indicator + '''
                </a>'''

                        body += '''
            </div>
        </div>
    </div>'''

                    if admin_modules:
                        body += '''
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">System Administration</h3>
            <p class="card-subtitle">Administrative tools and settings</p>
        </div>
        <div class="card-content">
            <div class="module-grid">'''

                        for module in admin_modules:
                            display_name = get_module_display_name(module)
                            body += '''
                <a href="''' + module + '''" class="module-card admin-module" onclick="URLjump('''' + module + '''', this); return false;" title="Access ''' + display_name + ''' module">
                    <div class="module-name">''' + display_name + '''</div>
                </a>'''

                        body += '''
            </div>
        </div>
    </div>'''
            else:
                # Auto-redirect to login if no public modules available
                if not to_show_above_login and to_show_for_login:
                    url_to_use = make_home_url_from_environ(environ)
                    redirect = url_to_use + '/login'
                    other = {'status': '303 See Other', 'response_header': [('Location', redirect)]}
                else:
                    # Development login shortcuts for testing
                    body += '''
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Development Login</h3>
            <p class="card-subtitle">Quick login for testing (development only)</p>
        </div>
        <div class="card-content">
            <div class="module-grid">
                <a href="index?dev_login=admin" class="module-card admin-module" onclick="URLjump('index?dev_login=admin', this); return false;" title="Login as Administrator">
                    <div class="module-name">Login as Admin</div>
                </a>
                <a href="index?dev_login=user" class="module-card" onclick="URLjump('index?dev_login=user', this); return false;" title="Login as Regular User">
                    <div class="module-name">Login as User</div>
                </a>
                <a href="index?dev_login=dev" class="module-card primary" onclick="URLjump('index?dev_login=dev', this); return false;" title="Login as Developer">
                    <div class="module-name">Login as Dev</div>
                </a>
            </div>
        </div>
    </div>'''

        except:
            body += '''
    <div class="error-section">
        <div class="section-title">System Error</div>
        <div class="error-content">
            <strong>Exception on permissions call:</strong><br>
            ''' + str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")) + '''
        </div>
    </div>'''

        body += '''
</div>'''

    except Exception as e:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body

# ====================================
def make_body_GET(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}


    if True:
        user = login.get_current_user(environ)
        data_store_content = datastore.all_datastore()
        permissions_cache = permissions.make_permissions_cache_from_datastore(data_store_content)
        not_allow_list = make_not_allow_list_from_datastore(data_store_content)
        modules_found_file_content = ''
        try:
            modules_found_file_content = open(s_loader_module_save_path,'r').read()
        except:
            pass
        module_permissions = permissions.make_module_permissions_for_user_from_permissions_cache(user,permissions_cache,not_allow_list,modules_found_file_content)
    else:
        module_permissions = permissions.get_module_permissions_for_environ(environ)

    body = make_body_GET_content(module_permissions, environ)

    return body, other


# ====================================
def make_body_parts_for_service_controls(the_service, restart_only=False):
    # ====================================
    body = ''

    for next_Value in ['start', 'stop', 'restart']:
        color = "(255, 0, 0, 0.0)"

        body += '<td title="' + next_Value + '" style="background-color:rgba' + color + '">'

        make_it = True
        if restart_only:
            if next_Value != 'restart':
                make_it = False

        if make_it:
            body += '<form method="post" action="">'
            body += '<select name="process_start_stop" id="process_start_stop" hidden>'
            body += '<option value="' + the_service + '" selected>' + the_service + '</option>'
            body += '</select>'

            body += '<select name="process_start_stop_value" id="process_start_stop_value" hidden>'
            body += '<option value="' + next_Value + '" selected>' + next_Value + '</option>'
            body += '</select>'
            body += '<center>'
            body += '<input type="submit" value="' + next_Value + '">'
            body += '</center>'
            body += '</form>'
        body += '</td>'

    return body


# Main is the loop for the "template-runner" that the service starts
# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1

        time.sleep(2)


# ====================================
def application(environ, start_response):
    # ====================================

    value_test = 0
    if 'HTTP_COOKIE' in environ:
        value_test = 1

    status = '200 OK'
    response_header = [('Content-type', 'text/html')]

    html = ''
    try:
        body, other = make_body(environ)
        status = other['status']
        head = ''
        if 'head' in other:
            head = other['head']
        response_header = other['response_header']
        if other['add_wrapper']:
            html += '<html>\n'
            if head:
                html += '<head>\n'
                html += head
                html += '</head>\n'
            html += '<body>\n'
        html += body
        if other['add_wrapper']:
            html += '</body>\n'
            html += '</html>\n'

    #        response_header.append(set_cookie_header('name_test', str(value_test)))
    #        response_header.append(set_cookie_header('name_test2', str(10+value_test)))

    except:
        html += '<html>\n' \
                '<body>\n'
        html += str(sys.version_info)
        html += str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
        html += '</body>\n' \
                '</html>\n'

    try:
        html = organization.wrap_page_with_session(environ, html)
        start_response(status, response_header)
    except:
        # still on slicer01
        # allow non wrapped response
        start_response(status, response_header)

    return [html.encode()]


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_template(self):
        """
        (fill in here)
        """
        self.assertEqual(True, True)

# End of source file
