# Ignore_for_mass_testing
#!/usr/bin/env python3
"""
Realistic Mock System for Slicer WSGI Development
================================================

This provides a realistic authentication and permissions system that mimics
the production behavior, including login/logout functionality and proper
session management.
"""

import sys
import os
import time
import json

# Simple in-memory session storage for development
DEV_SESSIONS = {}
DEV_USERS = {
    'admin': {'password': 'admin', 'permissions': ['admin', 'read', 'write']},
    'user': {'password': 'user', 'permissions': ['read']},
    'dev': {'password': 'dev', 'permissions': ['admin', 'read', 'write']},
}

class DynamicMock:
    """A dynamic mock that returns sensible defaults for any missing attributes/methods"""

    def __init__(self, name="DynamicMock"):
        self._name = name

    def __getattr__(self, name):
        def mock_method(*args, **kwargs):
            print(f"[MOCK] Called {self._name}.{name}() with args={args}, kwargs={kwargs}")
            # Return different defaults based on common patterns
            if name.startswith('get_') or name.startswith('find_'):
                return {}
            elif name.startswith('is_') or name.startswith('has_') or name.startswith('check_'):
                return True
            elif name.startswith('list_') or name.startswith('search_'):
                return []
            else:
                return f"Mock result for {self._name}.{name}"

        return mock_method

    def __bool__(self):
        return True

    def __str__(self):
        return f"<{self._name} Mock>"

def get_cookie_contents_from_environ(environ):
    """Extract cookies from environment - copied from real login.py"""
    return_value = {}
    if 'HTTP_COOKIE' in environ:
        for each_item in environ['HTTP_COOKIE'].split(';'):
            item = each_item.split('=')
            if len(item) >= 2:
                return_value[item[0].strip()] = item[1].strip()
    return return_value

class RealisticMockLogin(DynamicMock):
    """Realistic login module that simulates actual authentication"""

    def __init__(self):
        super().__init__("login")
        self.session_timeout = 2 * 60 * 60  # 2 hours like production

    def get_current_user(self, environ, refresh_timeout=False):
        """Get current user from session - mimics real login.py behavior"""
        print(f"[DEBUG] login.get_current_user() called")
        addr = environ.get('REMOTE_ADDR', 'localhost')
        cookie_content = get_cookie_contents_from_environ(environ)

        # Check if there's a login session
        if 'login_time' not in cookie_content:
            return ''  # No session = no user (like production)

        session_id = cookie_content['login_time']
        session_key = f"{addr}_{session_id}"

        # Check if session exists and is valid
        if session_key in DEV_SESSIONS:
            session = DEV_SESSIONS[session_key]
            current_time = time.time()

            # Check if session has expired
            if current_time - session['last_touch'] < self.session_timeout:
                # Session is valid, optionally refresh it
                if refresh_timeout:
                    session['last_touch'] = current_time
                return session['username']
            else:
                # Session expired, clean it up
                del DEV_SESSIONS[session_key]

        return ''  # No valid session

    def create_session(self, username, addr, session_id):
        """Create a new session for a user"""
        session_key = f"{addr}_{session_id}"
        DEV_SESSIONS[session_key] = {
            'username': username,
            'last_touch': time.time(),
            'created': time.time()
        }

    def destroy_session(self, addr, session_id):
        """Destroy a session (logout)"""
        session_key = f"{addr}_{session_id}"
        if session_key in DEV_SESSIONS:
            del DEV_SESSIONS[session_key]

    def validate_credentials(self, username, password):
        """Check if username/password are valid"""
        # Validate inputs first
        if not username or not password:
            return False
        if username not in DEV_USERS:
            return False
        if 'password' not in DEV_USERS[username]:
            return False

        # Safe access after validation
        user_data = DEV_USERS[username]
        stored_password = user_data['password']

        # Explicit comparison with condition
        if stored_password == password:
            return True
        else:
            return False

    def get_user_permissions(self, username):
        """Get permissions for a user"""
        if username in DEV_USERS:
            return DEV_USERS[username]['permissions']
        return []

class RealisticMockPermissions(DynamicMock):
    """Realistic permissions module that mimics production behavior"""

    def __init__(self):
        super().__init__("permissions")
        self.login_module = None  # Will be set after creation

    def permission_prefix_allowed(self, environ, prefix):
        """Check if user has permission for a prefix"""
        # For development, always allow development_ prefix
        if prefix == 'development_':
            return True
        return False

    def log_page_allowed(self, environ, service, other=None):
        """Log that page access was allowed"""
        print(f"[MOCK] Page access allowed for service: {service}")
        return True

    def permission_allowed(self, environ, permission_name):
        """Check if a specific permission is allowed"""
        print(f"[MOCK] Checking permission: {permission_name}")
        # For development, allow most permissions
        return True

    def permission_site_allowed(self, environ, site):
        """Check if access to a specific site is allowed"""
        print(f"[MOCK] Checking site permission: {site}")
        # For development, allow access to all sites
        return True

    def make_permissions_cache_from_datastore(self, datastore_content):
        """Create permissions cache from datastore"""
        print(f"[DEBUG] permissions.make_permissions_cache_from_datastore() called")
        return {
            'admin_users': ['admin', 'dev'],
            'regular_users': ['user'],
            'cache_created': True
        }

    def make_module_permissions_for_user_from_permissions_cache(self, user, permissions_cache, not_allow_list, modules_found_file_content):
        """Create module permissions based on user status - this is the key function!"""
        print(f"[DEBUG] permissions.make_module_permissions_for_user_from_permissions_cache() called with user={user}")

        try:
            # If modules_found_file_content is empty, create a default list of available modules
            if not modules_found_file_content or modules_found_file_content.strip() == '':
                modules_found_file_content = '''index
login
loader
datastore
debug
management
organization
profiles
sites
timezones
htmlfiles
deviceupload
ldapsupport
videos
certificates
permissions
investigate
jamf
address2location
thirdparty
multimedia
dataport
speedtest
dashboard
reports'''

            # Default lists (what shows when not logged in)
            to_show_above_login = []  # Public modules (shown above login)
            to_show_for_login = ['login']  # Login-related modules
            to_show_below_login = []  # Modules shown after login

            # If user is logged in (non-empty string), show authenticated content
            if user and isinstance(user, dict) and user.get('username'):
                current_user = user['username']
                user_permissions = self.login_module.get_user_permissions(current_user) if self.login_module else ['read']

                # Authenticated user gets access to more modules
                to_show_above_login = ['index', 'dashboard', 'reports', 'timezones', 'videos']
                to_show_for_login = []  # Hide login when logged in

                # Modules available after login
                to_show_below_login = [
                    'datastore', 'debug', 'profiles', 'sites',
                    'htmlfiles', 'deviceupload', 'certificates',
                    'investigate', 'multimedia', 'speedtest'
                ]

                # Admin users get additional modules
                if 'admin' in user_permissions:
                    to_show_below_login.extend([
                        'loader', 'management', 'organization', 'permissions',
                        'ldapsupport', 'jamf', 'address2location', 'thirdparty', 'dataport'
                    ])

            elif user and isinstance(user, str) and user:
                # User is a simple string (username)
                current_user = user
                user_permissions = self.login_module.get_user_permissions(current_user) if self.login_module else ['read']

                to_show_above_login = ['index', 'dashboard', 'reports', 'timezones', 'videos']
                to_show_for_login = []
                to_show_below_login = [
                    'datastore', 'debug', 'profiles', 'sites',
                    'htmlfiles', 'deviceupload', 'certificates',
                    'investigate', 'multimedia', 'speedtest'
                ]

                if 'admin' in user_permissions:
                    to_show_below_login.extend([
                        'loader', 'management', 'organization', 'permissions',
                        'ldapsupport', 'jamf', 'address2location', 'thirdparty', 'dataport'
                    ])
            else:
                # No user logged in - show public modules that all users can access
                current_user = None
                # Public modules accessible without login (like in production)
                to_show_above_login = ['index', 'dashboard', 'reports', 'timezones', 'videos']
                to_show_for_login = ['login']
                to_show_below_login = []

            result = {
                'to_show_above_login': to_show_above_login,
                'to_show_for_login': to_show_for_login,
                'to_show_below_login': to_show_below_login,
                'current_user': current_user,
                'permissions_cache': permissions_cache,
                'user_has_admin': bool(current_user and self.login_module and 'admin' in self.login_module.get_user_permissions(current_user)),
                'user_permissions': self.login_module.get_user_permissions(current_user) if self.login_module and current_user else [],
                'modules_available': to_show_above_login + to_show_below_login,
                'allowed_modules': to_show_above_login + to_show_below_login,
                'admin_modules': ['loader', 'management', 'organization', 'permissions'] if current_user else [],
                'read_only_modules': []
            }

            print(f"[DEBUG] Returning permissions structure: {result}")
            return result

        except Exception as e:
            print(f"[ERROR] Exception in make_module_permissions_for_user_from_permissions_cache: {e}")
            import traceback
            traceback.print_exc()
            # Return a simple fallback structure
            return {
                'to_show_above_login': ['index', 'dashboard', 'reports', 'timezones', 'videos'],
                'to_show_for_login': ['login'],
                'to_show_below_login': [],
                'current_user': None,
                'permissions_cache': {},
                'user_has_admin': False,
                'user_permissions': [],
                'modules_available': ['index', 'dashboard', 'reports', 'timezones', 'videos'],
                'allowed_modules': ['index', 'dashboard', 'reports', 'timezones', 'videos'],
                'admin_modules': [],
                'read_only_modules': []
            }

    def get_module_permissions_for_environ(self, environ):
        """Get module permissions directly from environment"""
        # This would be used as fallback
        return {
            'to_show_above_login': [],
            'to_show_for_login': ['login'],
            'to_show_below_login': [],
            'current_user': None,
            'allowed_modules': ['login'],
            'admin_modules': [],
            'read_only_modules': [],
            'user_has_admin': False
        }

class RealisticMockOrganization(DynamicMock):
    """Organization module with realistic config"""

    def __init__(self):
        super().__init__("organization")

    def get_config(self, service):
        """Mock configuration that matches production structure"""
        return {
            'service': service,
            'debug': True,
            'site_title': 'Slicer 2.0 (Development)',
            'build': 'dev-build-001',
            'log_path': '/tmp/slicer_logs',
            'data_path': '/tmp/slicer_data',
            'base_log_path': '/tmp/slicer/login/',
            'time_to_remain_valid': 2 * 60 * 60,  # 2 hours
            'login_authentication': {
                'authentication_type': 'user_list',  # Use simple user list for dev
                'user_domain': '@dev.local',
                'user_domain_list': ['@dev.local'],
            },
            'user_list': DEV_USERS,
            # Dashboard specific config
            'sites_to_drop': {},  # Empty dict means no sites to drop
            'days_to_keep_dashboard_data': 60,
            # Reports specific config
            'checkin_file_root': '/tmp/slicer_checkin/',
            'datadrop_save_path': '/tmp/slicer_datadrop/',
            'statistics_save_path': '/tmp/slicer_statistics/',
            # Other common config items that might be needed
            'max_rows_to_show': 1000,
            'refresh_interval': 300,
        }

    def make_all_dirs(self, config):
        """Mock directory creation"""
        pass

    def wrap_page_with_session(self, environ, html):
        """Mock session wrapper - just return the HTML as-is"""
        return html

class RealisticMockDatastore(DynamicMock):
    """Datastore with realistic data"""

    def __init__(self):
        super().__init__("datastore")
        self.data = {}

    def get_data(self, key):
        return self.data.get(key, f"Mock data for {key}")

    def set_data(self, key, value):
        self.data[key] = value
        return True

    def get_value(self, key):
        return self.data.get(key, '')

    def set_value(self, key, value, who='system'):
        self.data[key] = value
        return True

    def search(self, query):
        return []

    def all_datastore(self):
        """Mock datastore content that looks realistic"""
        print(f"[DEBUG] datastore.all_datastore() called")
        base_data = {
            'users': [
                {'username': 'admin', 'permissions': ['admin']},
                {'username': 'dev', 'permissions': ['admin']},
                {'username': 'user', 'permissions': ['read']}
            ],
            'settings': {'debug': True, 'environment': 'development'},
            'permissions': {
                'admin': ['read', 'write', 'admin'],
                'dev': ['read', 'write', 'admin'],
                'user': ['read']
            },
            'organizations': [{'name': 'Development Org', 'id': 'dev_org'}]
        }

        # Add any data that was set via set_value
        base_data.update(self.data)
        return base_data

    def trust(self):
        """Mock trust function - return True for development"""
        return True

    def get_value_stored(self, data_store_content, key):
        """Mock function to get a value from stored datastore content"""
        print(f"[MOCK] datastore.get_value_stored(data_store_content, '{key}') called")
        # Return mock values based on key patterns
        if 'device_retired_' in key:
            return False  # Device not retired
        elif 'status_' in key:
            return 'active'
        elif 'last_seen_' in key:
            return '2024-04-01 12:00:00'
        else:
            return f"mock_value_for_{key}"

# Create instances
login = RealisticMockLogin()
permissions = RealisticMockPermissions()
organization = RealisticMockOrganization()
datastore = RealisticMockDatastore()

# Set cross-references
permissions.login_module = login

# Create other simple mocks
class SimpleMock(DynamicMock):
    pass

class DatamineMock(DynamicMock):
    """Mock for datamine module used in reports"""

    def __init__(self):
        super().__init__("datamine")

    def build_data_dictionary(self, seconds_for_current):
        """Mock data dictionary builder with comprehensive coverage"""
        print(f"[MOCK] datamine.build_data_dictionary({seconds_for_current})")

        # Create a dynamic dictionary class that handles missing keys
        class DynamicDataItems(dict):
            def __getitem__(self, key):
                if key in self:
                    return super().__getitem__(key)
                else:
                    # Return a default structure for any missing key
                    print(f"[MOCK] Creating dynamic data item for key: {key}")
                    return {'source': f'device_{key}', 'filter': f'{key}_filter'}

        data_items = DynamicDataItems()

        # Common device/column identifiers
        common_identifiers = [
            'id', 'idr', 'serial', 'status', 'ip', 'ring', 'name', 'tag', 'boot',
            'uptime', 'monitor', 'image', 'grab', 'index', 'seconds_monitor',
            'stats_load_report', 'wlan0mac', 'wlan1mac', 'logging_version',
            'network_version', 'service_pack', 'device_retired', 'calc_site',
            'calc_site_name', 'profile', 'profile_screen_threshold', 'management_block'
        ]

        for identifier in common_identifiers:
            data_items[identifier] = {
                'source': f'device_{identifier}',
                'filter': f'{identifier}_filter'
            }

        # Add actual device entries
        for i in range(1, 6):  # device1 through device5
            device_id = f'device{i}'
            data_items[device_id] = {
                'serial': f'DEV00{i}',
                'status': 'active' if i <= 3 else 'inactive',
                'filter': 'active' if i <= 3 else 'inactive'
            }

        # Add data types
        data_items.update({
            'logging_data': {'type': 'logs', 'count': 1000, 'filter': 'logs'},
            'performance_data': {'type': 'performance', 'count': 500, 'filter': 'performance'}
        })

        return {
            'data_items': data_items,
            's_all_pi_services': {
                'service1': {'name': 'Service 1', 'status': 'running'},
                'service2': {'name': 'Service 2', 'status': 'stopped'},
                'service3': {'name': 'Service 3', 'status': 'running'}
            },
            'time_range': seconds_for_current,
            'summary': 'Mock data dictionary for development',
            'total_devices': 5,
            'active_devices': 3
        }

class RingsMock(DynamicMock):
    """Mock for rings module used in reports and other modules"""

    def __init__(self):
        super().__init__("rings")

    def get_content_of_versions(self, *args, **kwargs):
        """Mock function to get content of versions"""
        print(f"[MOCK] rings.get_content_of_versions() called with args={args}, kwargs={kwargs}")
        return {
            'versions': {
                'v1.0.0': {'date': '2024-01-01', 'features': ['Initial release']},
                'v1.1.0': {'date': '2024-02-01', 'features': ['Bug fixes', 'Performance improvements']},
                'v1.2.0': {'date': '2024-03-01', 'features': ['New dashboard features', 'Security updates']},
                'v2.0.0': {'date': '2024-04-01', 'features': ['Major UI overhaul', 'New reporting system']}
            },
            'latest_version': 'v2.0.0',
            'total_versions': 4,
            'content_summary': 'Mock version content for development'
        }

    def extract_service_pack_build_list(self, content_of_versions):
        """Mock function to extract service pack build list from version content"""
        print(f"[MOCK] rings.extract_service_pack_build_list() called with content={content_of_versions}")
        return [
            {'build': '001', 'version': 'v1.0.0', 'date': '2024-01-01', 'type': 'release'},
            {'build': '002', 'version': 'v1.1.0', 'date': '2024-02-01', 'type': 'patch'},
            {'build': '003', 'version': 'v1.2.0', 'date': '2024-03-01', 'type': 'minor'},
            {'build': '004', 'version': 'v2.0.0', 'date': '2024-04-01', 'type': 'major'}
        ]

    def extract_service_pack_release_notes(self, content_of_versions):
        """Mock function to extract service pack release notes from version content"""
        print(f"[MOCK] rings.extract_service_pack_release_notes() called with content={content_of_versions}")
        return {
            'v1.0.0': 'Initial release with core functionality',
            'v1.1.0': 'Bug fixes and performance improvements',
            'v1.2.0': 'New dashboard features and security updates',
            'v2.0.0': 'Major UI overhaul and new reporting system'
        }

    def lookup_rings_to_service_pack(self):
        """Mock function to lookup rings to service pack mapping"""
        print(f"[MOCK] rings.lookup_rings_to_service_pack() called")
        return {
            'ring_1': {'service_pack': 'SP001', 'version': 'v1.0.0', 'status': 'active'},
            'ring_2': {'service_pack': 'SP002', 'version': 'v1.1.0', 'status': 'active'},
            'ring_3': {'service_pack': 'SP003', 'version': 'v1.2.0', 'status': 'testing'},
            'ring_4': {'service_pack': 'SP004', 'version': 'v2.0.0', 'status': 'development'}
        }

    def get_service_pack_info(self, service_pack_id=None):
        """Mock function to get service pack information"""
        print(f"[MOCK] rings.get_service_pack_info({service_pack_id}) called")
        all_packs = {
            'SP001': {'version': 'v1.0.0', 'date': '2024-01-01', 'status': 'released'},
            'SP002': {'version': 'v1.1.0', 'date': '2024-02-01', 'status': 'released'},
            'SP003': {'version': 'v1.2.0', 'date': '2024-03-01', 'status': 'testing'},
            'SP004': {'version': 'v2.0.0', 'date': '2024-04-01', 'status': 'development'}
        }
        return all_packs.get(service_pack_id, all_packs) if service_pack_id else all_packs

    def get_ring_status(self, ring_id=None):
        """Mock function to get ring status"""
        print(f"[MOCK] rings.get_ring_status({ring_id}) called")
        all_rings = {
            'ring_1': {'devices': 25, 'status': 'healthy', 'last_update': '2024-04-01'},
            'ring_2': {'devices': 50, 'status': 'healthy', 'last_update': '2024-04-01'},
            'ring_3': {'devices': 75, 'status': 'updating', 'last_update': '2024-04-01'},
            'ring_4': {'devices': 10, 'status': 'testing', 'last_update': '2024-04-01'}
        }
        return all_rings.get(ring_id, all_rings) if ring_id else all_rings

    def device_ring_calculated(self, device_id, pre_load_ring=None):
        """Mock function to calculate device ring"""
        print(f"[MOCK] rings.device_ring_calculated({device_id}, {pre_load_ring}) called")
        # Return a simple string ring name (not a dict) for string replacement
        ring_number = hash(str(device_id)) % 4 + 1
        return f'ring_{ring_number}'

    def calculate_service_pack(self, service_versions, service_pack_list):
        """Mock function to calculate service pack"""
        print(f"[MOCK] rings.calculate_service_pack() called with {len(service_versions) if service_versions else 0} versions")
        return {
            'service_pack_id': 'SP001',
            'version': 'v1.0.0',
            'calculated_from': service_versions,
            'status': 'active',
            'compatibility_score': 95
        }

    def calculate_rings_vs_service_pack(self, rings_to_service_pack, service_pack_list, service_pack_release_notes):
        """Mock function to calculate rings vs service pack comparison"""
        print(f"[MOCK] rings.calculate_rings_vs_service_pack() called")
        return {
            'summary': {
                'total_rings': 4,
                'total_service_packs': 4,
                'active_rings': 2,
                'testing_rings': 1,
                'development_rings': 1
            },
            'rings_breakdown': {
                'ring_1': {'service_pack': 'SP001', 'devices': 25, 'status': 'stable'},
                'ring_2': {'service_pack': 'SP002', 'devices': 50, 'status': 'stable'},
                'ring_3': {'service_pack': 'SP003', 'devices': 75, 'status': 'testing'},
                'ring_4': {'service_pack': 'SP004', 'devices': 10, 'status': 'development'}
            },
            'service_pack_adoption': {
                'SP001': {'rings': ['ring_1'], 'total_devices': 25},
                'SP002': {'rings': ['ring_2'], 'total_devices': 50},
                'SP003': {'rings': ['ring_3'], 'total_devices': 75},
                'SP004': {'rings': ['ring_4'], 'total_devices': 10}
            }
        }

    def get_services_from_list_of_sp(self, rings_vs_service_pack, service_pack_list):
        """Mock function to get services from list of service packs"""
        print(f"[MOCK] rings.get_services_from_list_of_sp() called")
        return {
            'pi_services': [
                {'name': 'Service A', 'version': 'v1.0', 'status': 'running'},
                {'name': 'Service B', 'version': 'v1.1', 'status': 'running'},
                {'name': 'Service C', 'version': 'v1.2', 'status': 'stopped'},
                {'name': 'Service D', 'version': 'v2.0', 'status': 'testing'}
            ],
            'total_services': 4,
            'running_services': 2,
            'service_packs_used': service_pack_list
        }

rings = RingsMock()
scan = SimpleMock("scan")
tasks = SimpleMock("tasks")
upload = SimpleMock("upload")
codeupload = SimpleMock("codeupload")
intake = SimpleMock("intake")
htmlfiles = SimpleMock("htmlfiles")
certificates = SimpleMock("certificates")
watchdog = SimpleMock("watchdog")
dashboard = SimpleMock("dashboard")
settings = SimpleMock("settings")
datamine = DatamineMock()  # Add datamine mock

class CodeuploadMock(DynamicMock):
    """Mock for codeupload module used in reports"""

    def __init__(self):
        super().__init__("codeupload")

    def get_pi_services(self):
        """Mock function to get PI services"""
        print(f"[MOCK] codeupload.get_pi_services() called")
        return [
            {'name': 'Authentication Service', 'status': 'running', 'version': '1.0.0'},
            {'name': 'Data Processing Service', 'status': 'running', 'version': '1.1.0'},
            {'name': 'Logging Service', 'status': 'running', 'version': '1.0.5'},
            {'name': 'Monitoring Service', 'status': 'stopped', 'version': '0.9.2'},
            {'name': 'Backup Service', 'status': 'running', 'version': '1.2.0'}
        ]

codeupload = CodeuploadMock()

class Address2LocationMock(DynamicMock):
    """Mock for address2location module"""

    def __init__(self):
        super().__init__("address2location")

    def get_all_locations_stored(self, data_store_content):
        """Mock function to get all stored locations"""
        print(f"[MOCK] address2location.get_all_locations_stored()")
        return {
            'location1': {'name': 'Main ice', 'address': '123 Main St'},
            'location2': {'name': 'Branch Office', 'address': '456 Oak Ave'},
            'location3': {'name': 'Remote Site', 'address': '789 Pine Rd'}
        }

    def location_stored(self, *args, **kwargs):
        """Mock function to check if a location is stored and return site info"""
        address = args[0] if args else 'unknown'
        print(f"[MOCK] address2location.location_stored() called with args={args}, kwargs={kwargs}")

        # Handle different types of address input
        if isinstance(address, dict):
            # If address is a dict, try to extract IP or use a default
            address_str = address.get('ip', address.get('address', 'unknown'))
        else:
            address_str = str(address)

        # Return a tuple (site_id, site_name) as expected by the reports module
        site_mappings = {
            '127.0.0.1': ('site_dev', 'Development Site'),
            'localhost': ('site_dev', 'Development Site'),
            '***********': ('site_main', 'Main Office'),
            '********': ('site_branch', 'Branch Office')
        }
        result = site_mappings.get(address_str, ('site_unknown', 'Unknown Location'))
        print(f"[MOCK] address2location.location_stored returning: {result}")
        return result

    def site_name_stored(self, data_store_content, site_id):
        """Mock function to get site name from site ID"""
        print(f"[MOCK] address2location.site_name_stored(data_store_content, '{site_id}') called")
        site_names = {
            'site_dev': 'Development Site',
            'site_main': 'Main Office',
            'site_branch': 'Branch Office',
            'site_unknown': 'Unknown Location',
            'site1': 'Site 1',
            'site2': 'Site 2',
            'site3': 'Site 3'
        }
        return site_names.get(site_id, f'Site {site_id}')

address2location = Address2LocationMock()

class ManagementMock(DynamicMock):
    """Mock for management module"""

    def __init__(self):
        super().__init__("management")

    def get_management_block_list(self, data_store_content):
        """Mock function to get management block list"""
        print(f"[MOCK] management.get_management_block_list()")
        return ['site1', 'site2', 'site3']  # Return list of site IDs

class ProfilesMock(DynamicMock):
    """Mock for profiles module"""

    def __init__(self):
        super().__init__("profiles")

    def make_profile_screen_threshold(self, data_store_content, profile_data):
        """Mock function to create profile screen threshold"""
        print(f"[MOCK] profiles.make_profile_screen_threshold() called with profile={profile_data}")
        return {
            'threshold': 85,
            'warning_level': 75,
            'critical_level': 90,
            'profile_type': 'standard',
            'calculated': True
        }

management = ManagementMock()
profiles = ProfilesMock()

# Create proper module objects
import types

def create_mock_module(name, instance):
    """Create a proper module object with the instance methods and dynamic behavior"""
    module = types.ModuleType(name)

    # Copy all methods and attributes from the instance to the module
    for attr_name in dir(instance):
        if not attr_name.startswith('_'):
            setattr(module, attr_name, getattr(instance, attr_name))

    # Add the dynamic __getattr__ behavior to handle missing attributes
    def module_getattr(name):
        if hasattr(instance, '__getattr__'):
            return instance.__getattr__(name)
        else:
            # Fallback dynamic behavior
            def mock_method(*args, **kwargs):
                print(f"[MOCK] Called {module.__name__}.{name}() with args={args}, kwargs={kwargs}")
                if name.startswith('get_') or name.startswith('find_'):
                    return {}
                elif name.startswith('is_') or name.startswith('has_') or name.startswith('check_'):
                    return True
                elif name.startswith('list_') or name.startswith('search_'):
                    return []
                else:
                    return f"Mock result for {module.__name__}.{name}"
            return mock_method

    module.__getattr__ = module_getattr
    return module

# Add them to sys.modules so they can be imported
sys.modules['login'] = create_mock_module('login', login)
sys.modules['organization'] = create_mock_module('organization', organization)
sys.modules['datastore'] = create_mock_module('datastore', datastore)
sys.modules['permissions'] = create_mock_module('permissions', permissions)
sys.modules['rings'] = create_mock_module('rings', rings)
sys.modules['scan'] = create_mock_module('scan', scan)
sys.modules['tasks'] = create_mock_module('tasks', tasks)
sys.modules['upload'] = create_mock_module('upload', upload)
sys.modules['codeupload'] = create_mock_module('codeupload', codeupload)
sys.modules['intake'] = create_mock_module('intake', intake)
sys.modules['htmlfiles'] = create_mock_module('htmlfiles', htmlfiles)
sys.modules['certificates'] = create_mock_module('certificates', certificates)
sys.modules['watchdog'] = create_mock_module('watchdog', watchdog)
sys.modules['dashboard'] = create_mock_module('dashboard', dashboard)
sys.modules['settings'] = create_mock_module('settings', settings)
sys.modules['profiles'] = create_mock_module('profiles', profiles)
sys.modules['datamine'] = create_mock_module('datamine', datamine)
sys.modules['codeupload'] = create_mock_module('codeupload', codeupload)
sys.modules['address2location'] = create_mock_module('address2location', address2location)
sys.modules['management'] = create_mock_module('management', management)

# Add global variables that some modules expect to be available
# These are module-level variables that get imported by WSGI modules
sites_to_drop = {}
days_to_keep_dashboard_data = 60
service_config = organization.get_config('dashboard')

print("[OK] Loaded realistic mock modules for development")
print("[OK] Available test users: admin/admin, user/user, dev/dev")
