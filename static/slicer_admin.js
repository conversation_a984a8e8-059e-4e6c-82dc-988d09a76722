// Professional navigation functionality
function URLjump(jumpLocation, buttonElement = null) {
    if (buttonElement) {
        const originalText = buttonElement.innerHTML;
        buttonElement.innerHTML = 'Loading...';
        buttonElement.style.pointerEvents = 'none';
        buttonElement.style.opacity = '0.7';

        setTimeout(() => {
            buttonElement.innerHTML = originalText;
            buttonElement.style.pointerEvents = 'auto';
            buttonElement.style.opacity = '1';
        }, 5000);
    }

    location.href = jumpLocation;
}

document.addEventListener('DOMContentLoaded', function() {
    // Add keyboard shortcuts for power users
    document.addEventListener('keydown', function(e) {
        if (e.altKey) {
            switch(e.key) {
                case 'h': e.preventDefault(); URLjump('index'); break;
                case 'd': e.preventDefault(); URLjump('dashboard'); break;
                case 'r': e.preventDefault(); URLjump('reports'); break;
                case 'l': e.preventDefault(); URLjump('login'); break;
            }
        }
    });

    // Add focus management for accessibility
    const buttons = document.querySelectorAll('.module-button');
    buttons.forEach((button, index) => {
        button.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                button.click();
            }
        });
    });

    console.log('Slicer Professional Interface ready');
});
