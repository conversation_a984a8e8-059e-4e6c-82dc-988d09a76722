/* Modern Enterprise Admin Styles */
:root {
    --primary-color: #1a73e8;
    --secondary-color: #5f6368;
    --success-color: #137333;
    --warning-color: #ea4335;
    --surface-color: #ffffff;
    --background-color: #f8f9fa;
    --border-color: #dadce0;
    --text-primary: #202124;
    --text-secondary: #5f6368;
    --border-radius: 8px;
    --shadow-sm: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);
    --shadow-md: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 2px 6px 2px rgba(60, 64, 67, 0.15);
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Google Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.5;
    font-size: 14px;
    min-height: 100vh;
}

.admin-layout {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.admin-header {
    background: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    padding: 0 24px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-sm);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 24px;
}

.logo {
    font-size: 20px;
    font-weight: 500;
    color: var(--text-primary);
    text-decoration: none;
}

.build-badge {
    background: #e8f0fe;
    color: var(--primary-color);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    border-radius: 20px;
    background: #f1f3f4;
    border: 1px solid transparent;
    transition: all 0.2s ease;
    cursor: pointer;
}

.user-menu:hover {
    background: #e8eaed;
    border-color: var(--border-color);
}

.user-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 500;
    font-size: 12px;
}

.user-name {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 13px;
}

.main-content {
    flex: 1;
    padding: 32px 24px;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

.content-section {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    margin-bottom: 24px;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.section-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
    background: #fafbfc;
}

.section-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary);
    margin: 0;
}

.section-subtitle {
    font-size: 13px;
    color: var(--text-secondary);
    margin-top: 4px;
}

.section-content {
    padding: 24px;
}

.module-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
}

.module-card {
    display: block;
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    text-decoration: none;
    color: var(--text-primary);
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.module-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.module-card.primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1557b0 100%);
    color: white;
    border-color: var(--primary-color);
}

.module-card.success {
    background: linear-gradient(135deg, var(--success-color) 0%, #0f5132 100%);
    color: white;
    border-color: var(--success-color);
}

.module-card.warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #c5221f 100%);
    color: white;
    border-color: var(--warning-color);
}

.module-title {
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 8px;
    line-height: 1.3;
}

.module-description {
    font-size: 12px;
    opacity: 0.8;
    line-height: 1.4;
}

.status-banner {
    background: linear-gradient(90deg, #fef7cd 0%, #fef3cd 100%);
    border: 1px solid #f9cf58;
    border-left: 4px solid #f9ab00;
    border-radius: var(--border-radius);
    padding: 16px 20px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.status-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    flex-shrink: 0;
}

.status-icon.untrusted {
    background: var(--warning-color);
}

.status-icon.trusted {
    background: var(--success-color);
}

.status-content {
    flex: 1;
}

.status-title {
    font-weight: 500;
    color: #7c2d12;
    margin-bottom: 4px;
}

.status-message {
    font-size: 13px;
    color: #92400e;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    text-decoration: none;
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-top: 8px;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: #1557b0;
    border-color: #1557b0;
}

.btn-danger {
    background: var(--warning-color);
    color: white;
    border-color: var(--warning-color);
}

.btn-danger:hover {
    background: #c5221f;
    border-color: #c5221f;
}

.btn-warning {
    background: #f59e0b;
    color: white;
    border-color: #f59e0b;
}

.btn-warning:hover {
    background: #d97706;
    border-color: #d97706;
}

.error-section {
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-left: 4px solid var(--warning-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 24px;
}

.error-title {
    color: #991b1b;
    font-weight: 500;
    margin-bottom: 8px;
}

.error-content {
    color: #7f1d1d;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
    font-size: 12px;
    line-height: 1.5;
    background: rgba(239, 68, 68, 0.05);
    padding: 12px;
    border-radius: 4px;
    overflow-x: auto;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .module-grid {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    }
}

@media (max-width: 768px) {
    .admin-header {
        padding: 0 16px;
        height: 56px;
    }

    .header-left {
        gap: 16px;
    }

    .logo {
        font-size: 18px;
    }

    .main-content {
        padding: 24px 16px;
    }

    .module-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: 12px;
    }

    .module-card {
        padding: 16px;
    }

    .user-name {
        display: none;
    }
}

@media (max-width: 480px) {
    .build-badge {
        display: none;
    }

    .module-grid {
        grid-template-columns: 1fr 1fr;
        gap: 8px;
    }

    .module-card {
        padding: 12px;
    }

    .module-title {
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .module-grid { grid-template-columns: repeat(auto-fill, minmax(120px, 1fr)); }
    .slicer-header { padding: 24px 16px; }
    .nav-container { padding: 16px; }
}

/* Status Badge Styles */
.status-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 4px;
}

.status-success {
    background: #dcfce7;
    color: #166534;
}

.status-warning {
    background: #fef3c7;
    color: #92400e;
}

.status-error {
    background: #fee2e2;
    color: #991b1b;
}

/* Module Card Variants */
.module-card.admin-module {
    border-left: 3px solid #dc2626;
    background: linear-gradient(135deg, #fef2f2 0%, #ffffff 100%);
}

.module-card.admin-module:hover {
    border-left-color: #b91c1c;
    background: linear-gradient(135deg, #fee2e2 0%, #fef2f2 100%);
}

.module-card.primary {
    border-left: 3px solid #2563eb;
    background: linear-gradient(135deg, #eff6ff 0%, #ffffff 100%);
}

.module-card.primary:hover {
    border-left-color: #1d4ed8;
    background: linear-gradient(135deg, #dbeafe 0%, #eff6ff 100%);
}

/* Alert Styles */
.alert {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px 20px;
    border-radius: var(--border-radius);
    margin-bottom: 24px;
    border-left: 4px solid;
}

.alert-warning {
    background: #fffbeb;
    border-left-color: #f59e0b;
    color: #92400e;
}

.alert-icon {
    font-size: 18px;
    line-height: 1;
    margin-top: 2px;
}

.alert-content {
    flex: 1;
}

.alert-content strong {
    display: block;
    font-weight: 600;
    margin-bottom: 4px;
}

.alert-content p {
    margin: 0 0 12px 0;
    font-size: 14px;
    line-height: 1.5;
}
