# Ignore_for_mass_testing
#!/usr/bin/env python3
"""
Development WSGI Server for Slicer Application
==============================================

This script creates a development server using wsgiref.simple_server
to test the Slicer WSGI applications locally without needing Apache/mod_wsgi.

Usage:
    python dev_server.py

Then access the applications at:
    http://localhost:8000/index
    http://localhost:8000/loader
    http://localhost:8000/datastore
    http://localhost:8000/debug
    etc.

The server will route requests to the appropriate WSGI application based on the URL path.
"""

import sys
import os
import importlib
from wsgiref.simple_server import make_server
from urllib.parse import parse_qs

# Import mock modules to prevent import errors in WSGI applications
try:
    import mock_modules
except ImportError:
    print("Warning: mock_modules.py not found. Some WSGI applications may fail to load.")


class SlicerWSGIRouter:
    """
    A simple WSGI router that dispatches requests to different Slicer WSGI applications
    based on the URL path.
    """

    def __init__(self, config=None):
        self.applications = {}
        self.config = config or {
            'production_path': '/var/www/html/',
            'simulate_https': False,
            'server_name': 'localhost',
            'debug_mode': True,
            'mock_filesystem': True
        }
        self.load_applications()

    def load_applications(self):
        """Load all available Slicer WSGI applications"""
        # List of available WSGI modules (you can add/remove as needed)
        wsgi_modules = [
            'slicer_wsgi_index',
            'slicer_wsgi_login',  # Added login module
            'slicer_wsgi_dashboard',  # Public module
            'slicer_wsgi_reports',  # Public module
            'slicer_wsgi_loader',
            'slicer_wsgi_datastore',
            'slicer_wsgi_debug',
            'slicer_wsgi_management',
            'slicer_wsgi_organization',
            'slicer_wsgi_profiles',
            'slicer_wsgi_sites',
            'slicer_wsgi_timezones',  # Public module
            'slicer_wsgi_htmlfiles',
            'slicer_wsgi_deviceupload',
            'slicer_wsgi_ldapsupport',
            'slicer_wsgi_videos',  # Public module
            'slicer_wsgi_certificates',
            'slicer_wsgi_permissions',
            'slicer_wsgi_investigate',
            'slicer_wsgi_jamf',
            'slicer_wsgi_address2location',
            'slicer_wsgi_thirdparty',
            'slicer_wsgi_multimedia',
            'slicer_wsgi_dataport',
            'slicer_wsgi_speedtest',
        ]

        for module_name in wsgi_modules:
            try:
                module = importlib.import_module(module_name)
                if hasattr(module, 'application'):
                    # Extract the service name from the module name
                    service_name = module_name.replace('slicer_wsgi_', '')

                    # Apply module-specific patches for common issues
                    self.patch_module_issues(module, service_name)

                    self.applications[service_name] = module.application
                    print(f"[OK] Loaded {service_name} from {module_name}")
                else:
                    print(f"[ERROR] Module {module_name} has no 'application' function")
            except ImportError as e:
                print(f"[ERROR] Could not import {module_name}: {e}")
            except Exception as e:
                print(f"[ERROR] Error loading {module_name}: {e}")

    def serve_static_file(self, environ, start_response, path_info):
        """Serve static CSS/JS files from the static directory"""
        file_path = os.path.join(os.path.dirname(__file__), path_info)

        if os.path.exists(file_path) and os.path.isfile(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Determine content type
                if file_path.endswith('.css'):
                    content_type = 'text/css'
                elif file_path.endswith('.js'):
                    content_type = 'application/javascript'
                else:
                    content_type = 'text/plain'

                status = '200 OK'
                headers = [('Content-type', content_type)]
                start_response(status, headers)
                return [content.encode('utf-8')]
            except Exception as e:
                # File read error
                status = '500 Internal Server Error'
                headers = [('Content-type', 'text/plain')]
                start_response(status, headers)
                return [f'Error reading file: {e}'.encode('utf-8')]
        else:
            # File not found
            status = '404 Not Found'
            headers = [('Content-type', 'text/plain')]
            start_response(status, headers)
            return [b'Static file not found']

    def patch_module_issues(self, module, service_name):
        """Apply patches to fix common module issues"""
        if service_name == 'dashboard':
            # Fix the sites_to_drop variable issue
            if not hasattr(module, 'sites_to_drop'):
                module.sites_to_drop = {}
            if not hasattr(module, 'days_to_keep_dashboard_data'):
                module.days_to_keep_dashboard_data = 60
        elif service_name == 'reports':
            # Fix missing module imports for reports
            import mock_modules
            if not hasattr(module, 'datamine'):
                module.datamine = mock_modules.datamine
            if not hasattr(module, 'address2location'):
                module.address2location = mock_modules.address2location
            if not hasattr(module, 'management'):
                module.management = mock_modules.management
            if not hasattr(module, 'profiles'):
                module.profiles = mock_modules.profiles
            if not hasattr(module, 'intake'):
                module.intake = mock_modules.intake

            # Patch directory operations to avoid filesystem dependencies
            import os
            original_listdir = os.listdir
            def mock_listdir(path):
                """Mock listdir to return fake files for development"""
                print(f"[MOCK] os.listdir({path}) called - returning mock files")
                if 'checkin' in path:
                    return ['device001.json', 'device002.json', 'device003.json']
                elif 'datadrop' in path:
                    return ['report_001.data', 'report_002.data']
                elif 'statistics' in path:
                    return ['stats_001.csv', 'stats_002.csv']
                else:
                    try:
                        return original_listdir(path)
                    except (FileNotFoundError, OSError):
                        return []  # Return empty list for missing directories

            # Replace os.listdir in the module's globals
            if hasattr(module, '__dict__'):
                module.__dict__['os'].listdir = mock_listdir

    def __call__(self, environ, start_response):
        """WSGI application callable that routes to the appropriate sub-application"""

        # Add missing WSGI environment variables that production Apache would provide
        # Do this early so all applications have access to these variables
        scheme = 'https' if self.config.get('simulate_https') else 'http'
        if 'REQUEST_SCHEME' not in environ:
            environ['REQUEST_SCHEME'] = scheme
        if 'HTTP_HOST' not in environ:
            # Get host and port from SERVER_NAME and SERVER_PORT
            server_name = environ.get('SERVER_NAME', self.config.get('server_name', 'localhost'))
            server_port = environ.get('SERVER_PORT', '443' if scheme == 'https' else '80')
            if server_port in ('80', '443'):
                environ['HTTP_HOST'] = server_name
            else:
                environ['HTTP_HOST'] = f"{server_name}:{server_port}"
        if 'REMOTE_ADDR' not in environ:
            environ['REMOTE_ADDR'] = '127.0.0.1'
        if 'REQUEST_URI' not in environ:
            query_string = environ.get('QUERY_STRING', '')
            path_info = environ.get('PATH_INFO', '/')
            environ['REQUEST_URI'] = path_info + ('?' + query_string if query_string else '')

        # Add production-like Apache/mod_wsgi environment variables
        if 'SERVER_SOFTWARE' not in environ:
            environ['SERVER_SOFTWARE'] = 'Apache/2.4.6 (CentOS) mod_wsgi/4.6.4 Python/3.6'
        if 'DOCUMENT_ROOT' not in environ:
            environ['DOCUMENT_ROOT'] = self.config.get('production_path', '/var/www/html')
        if 'SERVER_ADMIN' not in environ:
            environ['SERVER_ADMIN'] = 'webmaster@localhost'
        if 'SCRIPT_FILENAME' not in environ:
            environ['SCRIPT_FILENAME'] = self.config.get('production_path', '/var/www/html') + 'index.py'

        # Ensure production path is in sys.path for module imports
        production_path = self.config.get('production_path', '/var/www/html/')
        if production_path not in sys.path:
            sys.path.insert(0, production_path)

        path_info = environ.get('PATH_INFO', '').strip('/')

        # Handle static files first
        if path_info.startswith('static/'):
            return self.serve_static_file(environ, start_response, path_info)

        # Handle root path - redirect to index
        if not path_info:
            return self.redirect_to_index(environ, start_response)

        # Extract the service name from the path
        path_parts = path_info.split('/')
        service_name = path_parts[0]

        # Check if we have an application for this service
        if service_name in self.applications:
            # Modify the environ to pass the remaining path to the sub-application
            environ['PATH_INFO'] = '/' + '/'.join(path_parts[1:]) if len(path_parts) > 1 else '/'
            environ['SCRIPT_NAME'] = f'/{service_name}'

            # Call the appropriate WSGI application with error handling
            try:
                return self.applications[service_name](environ, start_response)
            except Exception as e:
                return self.handle_application_error(environ, start_response, service_name, e)
        else:
            # Service not found - show available services
            return self.show_available_services(environ, start_response)

    def redirect_to_index(self, environ, start_response):
        """Redirect root requests to /index"""
        status = '302 Found'
        headers = [('Location', '/index'), ('Content-Type', 'text/html')]
        start_response(status, headers)
        return [b'<html><body>Redirecting to <a href="/index">/index</a></body></html>']

    def show_available_services(self, environ, start_response):
        """Show a list of available services when an unknown path is requested"""
        status = '404 Not Found'
        headers = [('Content-Type', 'text/html')]

        html = '''
        <html>
        <head>
            <title>Slicer Development Server</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                h1 { color: #333; }
                .service-list { list-style-type: none; padding: 0; }
                .service-list li { margin: 10px 0; }
                .service-list a {
                    text-decoration: none;
                    color: #0066cc;
                    padding: 5px 10px;
                    border: 1px solid #ddd;
                    border-radius: 3px;
                    display: inline-block;
                }
                .service-list a:hover { background-color: #f0f0f0; }
                .error { color: #cc0000; margin-bottom: 20px; }
            </style>
        </head>
        <body>
            <h1>Slicer Development Server</h1>
        '''

        path_info = environ.get('PATH_INFO', '').strip('/')
        if path_info:
            html += f'<div class="error">Service "{path_info}" not found.</div>'

        html += '''
            <h2>Available Services:</h2>
            <ul class="service-list">
        '''

        for service_name in sorted(self.applications.keys()):
            html += f'<li><a href="/{service_name}">{service_name}</a></li>\n'

        html += '''
            </ul>
            <p><strong>Total services loaded:</strong> {}</p>
        </body>
        </html>
        '''.format(len(self.applications))

        start_response(status, headers)
        return [html.encode('utf-8')]

    def handle_application_error(self, environ, start_response, service_name, error):
        """Handle errors from WSGI applications"""
        import traceback

        status = '500 Internal Server Error'
        headers = [('Content-Type', 'text/html')]

        html = f'''
        <html>
        <head>
            <title>Error in {service_name}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                .error {{ color: #cc0000; background: #fff0f0; padding: 20px; border: 1px solid #ff9999; border-radius: 5px; }}
                .traceback {{ background: #f0f0f0; padding: 15px; margin: 20px 0; border-radius: 3px; font-family: monospace; font-size: 12px; }}
                .back-link {{ margin-top: 20px; }}
                .back-link a {{ color: #0066cc; text-decoration: none; }}
            </style>
        </head>
        <body>
            <h1>Error in {service_name} Service</h1>
            <div class="error">
                <strong>Error:</strong> {str(error)}
            </div>
            <div class="traceback">
                <strong>Traceback:</strong><br>
                {traceback.format_exc().replace(chr(10), '<br>').replace(' ', '&nbsp;')}
            </div>
            <div class="back-link">
                <a href="/"><- Back to service list</a>
            </div>
        </body>
        </html>
        '''

        start_response(status, headers)
        return [html.encode('utf-8')]


def run_dev_server(host='localhost', port=8000, production_mode=False, https_mode=False):
    """Run the development server"""
    print("=" * 60)
    print("Slicer WSGI Development Server")
    if production_mode:
        print("🔧 PRODUCTION SIMULATION MODE")
    print("=" * 60)

    # Create configuration
    config = {
        'production_path': '/var/www/html/',
        'simulate_https': https_mode,
        'server_name': host,
        'debug_mode': not production_mode,
        'mock_filesystem': not production_mode
    }

    # Create the router application
    app = SlicerWSGIRouter(config)

    if not app.applications:
        print("ERROR: No WSGI applications could be loaded!")
        print("Make sure you're running this from the correct directory.")
        return

    scheme = 'https' if https_mode else 'http'
    print(f"\nStarting server on {scheme}://{host}:{port}")
    print(f"Loaded {len(app.applications)} WSGI applications")
    if production_mode:
        print("⚠️  Production simulation: filesystem mocking disabled")
    print("\nPress Ctrl+C to stop the server")
    print("=" * 60)

    # Create and start the server
    try:
        with make_server(host, port, app) as httpd:
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n" + "=" * 60)
        print("Server stopped.")
        print("=" * 60)
    except Exception as e:
        print(f"\nERROR: Could not start server: {e}")


if __name__ == '__main__':
    # Parse command line arguments for host and port
    import argparse

    parser = argparse.ArgumentParser(description='Slicer WSGI Development Server')
    parser.add_argument('--host', default='localhost', help='Host to bind to (default: localhost)')
    parser.add_argument('--port', type=int, default=8000, help='Port to bind to (default: 8000)')
    parser.add_argument('--production', action='store_true',
                       help='Enable production simulation mode (disables mocking)')
    parser.add_argument('--https', action='store_true',
                       help='Simulate HTTPS environment variables')

    args = parser.parse_args()

    run_dev_server(args.host, args.port, args.production, args.https)
