/*
Slicer Enhanced Navigation CSS
==============================
This CSS should be embedded inline in each WSGI module for production compatibility.
No external files required.
*/

/* Reset and base styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

/* Header styling */
.slicer-header {
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.slicer-title {
    font-size: 2.5em;
    font-weight: bold;
    color: #2c3e50;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.build-info {
    color: #7f8c8d;
    font-size: 0.9em;
    margin-top: 5px;
}

/* Navigation container */
.nav-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    max-width: 1200px;
    margin: 0 auto;
}

/* Navigation grid */
.nav-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.nav-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    border-left: 4px solid;
}

.nav-section.public {
    border-left-color: #28a745;
}

.nav-section.auth {
    border-left-color: #007bff;
}

.nav-section.admin {
    border-left-color: #dc3545;
}

.section-title {
    font-size: 1.2em;
    font-weight: bold;
    margin: 0 0 15px 0;
    color: #2c3e50;
}

/* Module buttons */
.module-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
}

.module-button {
    display: block;
    padding: 12px 16px;
    background: #fff;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    text-decoration: none;
    color: #495057;
    text-align: center;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.module-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: #007bff;
    color: #007bff;
}

.module-button:active {
    transform: translateY(0);
}

/* Special button styles */
.module-button.primary {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.module-button.primary:hover {
    background: #0056b3;
    color: white;
}

.module-button.success {
    background: #28a745;
    color: white;
    border-color: #28a745;
}

.module-button.success:hover {
    background: #1e7e34;
    color: white;
}

.module-button.danger {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
}

.module-button.danger:hover {
    background: #c82333;
    color: white;
}

/* User info section */
.user-info {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 8px;
    padding: 15px;
    margin: 20px 0;
    text-align: center;
}

.user-info .username {
    font-weight: bold;
    color: #1976d2;
}

/* Status indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-indicator.trusted {
    background: #28a745;
}

.status-indicator.untrusted {
    background: #dc3545;
}

/* Responsive design */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }

    .slicer-title {
        font-size: 2em;
    }

    .nav-grid {
        grid-template-columns: 1fr;
    }

    .module-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }
}

/* Animation for loading states */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.nav-container {
    animation: fadeIn 0.5s ease-out;
}

/* Loading spinner */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
