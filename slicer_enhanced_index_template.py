# Ignore_for_mass_testing
"""
Enhanced Slicer Index Page Template
===================================

This template provides an enhanced navigation design that can be integrated
into the existing slicer_wsgi_index.py without breaking production compatibility.

Key Features:
- Inline CSS/JS (no external dependencies)
- Responsive design
- Accessibility support
- Permission-based navigation
- Professional styling
- Backward compatibility
"""

def make_enhanced_body_GET_content(module_permissions, environ={}):
    """
    Enhanced version of make_body_GET_content with improved styling and UX.
    This can replace the existing function in slicer_wsgi_index.py
    """
    to_show_above_login = module_permissions['to_show_above_login']
    to_show_for_login = module_permissions['to_show_for_login']
    to_show_below_login = module_permissions['to_show_below_login']
    current_user = module_permissions['current_user']

    # Get site configuration
    site_title = 'Slicer 2.0'
    site_build = 'dev-build-001'

    # Try to get from service_config if available
    try:
        if 'site_title' in service_config:
            site_title = service_config['site_title']
        if 'build' in service_config:
            site_build = service_config['build']
    except:
        pass

    # Build the enhanced page
    body = f'''
<style type="text/css">
{get_inline_css()}
</style>

<script>
{get_inline_javascript()}
</script>

<div class="slicer-header">
    <h1 class="slicer-title">{site_title}</h1>
    {f'<div class="build-info">Build: {site_build}</div>' if site_build else ''}
</div>

<div class="nav-container">
    {build_navigation_sections(to_show_above_login, to_show_for_login, to_show_below_login, current_user, environ)}
</div>
'''

    return body

def build_navigation_sections(to_show_above_login, to_show_for_login, to_show_below_login, current_user, environ):
    """Build the navigation sections based on user permissions"""

    sections = []

    # Available modules section (for both authenticated and non-authenticated users)
    if to_show_above_login:
        public_modules = build_module_grid(to_show_above_login, '')
        sections.append(f'''
        <div class="nav-section">
            <div class="section-title">Available Applications</div>
            <div class="module-grid">{public_modules}</div>
        </div>
        ''')

    # User section (when logged in)
    if current_user:
        if to_show_below_login:
            # Separate admin and regular modules
            admin_modules = [m for m in to_show_below_login if m in ['loader', 'management', 'organization', 'permissions', 'debug']]
            regular_modules = [m for m in to_show_below_login if m not in admin_modules]

            if regular_modules:
                regular_grid = build_module_grid(regular_modules, '')
                sections.append(f'''
                <div class="nav-section user">
                    <div class="section-title">User Applications</div>
                    <div class="module-grid">{regular_grid}</div>
                </div>
                ''')

            if admin_modules:
                admin_grid = build_module_grid(admin_modules, 'danger')
                sections.append(f'''
                <div class="nav-section admin">
                    <div class="section-title">System Administration</div>
                    <div class="module-grid">{admin_grid}</div>
                </div>
                ''')

    # Add system status if datastore is available
    try:
        import datastore
        trust_status = datastore.trust()
        status_class = 'trusted' if trust_status else 'untrusted'
        status_text = 'Trusted' if trust_status else 'Not Trusted'

        sections.append(f'''
        <div class="nav-section">
            <div class="section-title">System Status</div>
            <div style="padding: 10px;">
                <span class="status-indicator {status_class}"></span>
                Datastore: {status_text}
                {'' if trust_status else '<br><a href="datastore?action=begintrust" class="module-button" style="margin-top: 10px;">Begin Trust</a>'}
            </div>
        </div>
        ''')
    except:
        pass

    return '\\n'.join(sections)

def build_module_grid(modules, css_class=''):
    """Build HTML for a grid of module buttons"""
    if not modules:
        return ''

    buttons = []
    for module in modules:
        display_name = get_module_display_name(module)
        buttons.append(f'''
        <a href="{module}"
           class="module-button {css_class}"
           onclick="URLjump('{module}', this); return false;"
           title="Access {display_name} module">
            {display_name}
        </a>
        ''')

    return '\\n'.join(buttons)

def get_module_display_name(module):
    """Get user-friendly display names for modules"""
    display_names = {
        'index': 'Home',
        'dashboard': 'Dashboard',
        'reports': 'Reports',
        'login': 'Login',
        'datastore': 'Data Store',
        'debug': 'Debug',
        'loader': 'Loader',
        'management': 'Management',
        'organization': 'Organization',
        'profiles': 'Profiles',
        'sites': 'Sites',
        'timezones': 'Time Zones',
        'htmlfiles': 'HTML Files',
        'deviceupload': 'Upload',
        'ldapsupport': 'LDAP',
        'videos': 'Videos',
        'certificates': 'Certificates',
        'permissions': 'Permissions',
        'investigate': 'Investigate',
        'jamf': 'JAMF',
        'address2location': 'Locations',
        'thirdparty': 'Third Party',
        'multimedia': 'Multimedia',
        'dataport': 'Data Port',
        'speedtest': 'Speed Test'
    }

    return display_names.get(module, module.title())

def get_inline_css():
    """Return the CSS as a string for inline embedding"""
    return '''
/* Slicer Enhanced Navigation Styles */
* { box-sizing: border-box; }

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.slicer-header {
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.slicer-title {
    font-size: 2.5em;
    font-weight: bold;
    color: #2c3e50;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.build-info {
    color: #7f8c8d;
    font-size: 0.9em;
    margin-top: 5px;
}

.nav-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.nav-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    border-left: 4px solid;
}

.nav-section.public { border-left-color: #28a745; }
.nav-section.auth { border-left-color: #007bff; }
.nav-section.admin { border-left-color: #dc3545; }

.section-title {
    font-size: 1.2em;
    font-weight: bold;
    margin: 0 0 15px 0;
    color: #2c3e50;
}

.module-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
}

.module-button {
    display: block;
    padding: 12px 16px;
    background: #fff;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    text-decoration: none;
    color: #495057;
    text-align: center;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
}

.module-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: #007bff;
    color: #007bff;
}

.module-button.primary {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.module-button.primary:hover {
    background: #0056b3;
    color: white;
}

.module-button.success {
    background: #28a745;
    color: white;
    border-color: #28a745;
}

.module-button.success:hover {
    background: #1e7e34;
    color: white;
}

.module-button.danger {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
}

.module-button.danger:hover {
    background: #c82333;
    color: white;
}

.user-info {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    grid-column: 1 / -1;
}

.username {
    font-weight: bold;
    color: #1976d2;
    margin-bottom: 10px;
}

.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-indicator.trusted { background: #28a745; }
.status-indicator.untrusted { background: #dc3545; }

@media (max-width: 768px) {
    body { padding: 10px; }
    .slicer-title { font-size: 2em; }
    .nav-container {
        grid-template-columns: 1fr;
        padding: 20px;
    }
    .module-grid { grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); }
}

.loading {
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
'''

def get_inline_javascript():
    """Return the JavaScript as a string for inline embedding"""
    return '''
// Enhanced URL navigation with loading states
function URLjump(jumpLocation, buttonElement = null) {
    if (buttonElement) {
        const originalText = buttonElement.innerHTML;
        buttonElement.innerHTML = '<span class="loading"></span> Loading...';
        buttonElement.style.pointerEvents = 'none';

        setTimeout(() => {
            buttonElement.innerHTML = originalText;
            buttonElement.style.pointerEvents = 'auto';
        }, 5000);
    }

    location.href = jumpLocation;
}

// Initialize navigation enhancements
document.addEventListener('DOMContentLoaded', function() {
    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        if (e.altKey) {
            switch(e.key) {
                case 'h': e.preventDefault(); URLjump('index'); break;
                case 'd': e.preventDefault(); URLjump('dashboard'); break;
                case 'r': e.preventDefault(); URLjump('reports'); break;
            }
        }
    });

    // Add accessibility
    const buttons = document.querySelectorAll('.module-button');
    buttons.forEach((button, index) => {
        button.setAttribute('tabindex', index + 1);
        button.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                button.click();
            }
        });
    });

    console.log('Slicer Enhanced Navigation ready');
});
'''

# Integration instructions for existing code:
"""
To integrate this enhanced design into the existing slicer_wsgi_index.py:

1. Replace the existing make_body_GET_content function with make_enhanced_body_GET_content
2. Or modify the existing function to use the enhanced template

The design maintains full backward compatibility while providing:
- Professional, responsive design
- Better organization of modules
- Visual feedback and loading states
- Accessibility features
- Keyboard shortcuts
- Mobile-friendly layout
- No external dependencies
"""
