# Ignore_for_mass_testing
#!/usr/bin/env python3
"""
Simple Development Server for Individual Slicer WSGI Applications
================================================================

This script runs a single WSGI application for focused testing.

Usage:
    python simple_dev_server.py index
    python simple_dev_server.py loader
    python simple_dev_server.py datastore

Or modify the DEFAULT_SERVICE variable below to set a default.
"""

import sys
import importlib
from wsgiref.simple_server import make_server

# Import mock modules to prevent import errors in WSGI applications
try:
    import mock_modules
except ImportError:
    print("Warning: mock_modules.py not found. Some WSGI applications may fail to load.")

# Default service to run if none specified
DEFAULT_SERVICE = 'index'

def run_single_service(service_name, host='localhost', port=8000):
    """Run a single WSGI service"""

    module_name = f'slicer_wsgi_{service_name}'

    try:
        print(f"Loading {module_name}...")
        module = importlib.import_module(module_name)

        if not hasattr(module, 'application'):
            print(f"ERROR: {module_name} does not have an 'application' function")
            return

        original_app = module.application

        # Wrap the application to add missing environment variables
        def wrapped_app(environ, start_response):
            # Add missing WSGI environment variables
            if 'REQUEST_SCHEME' not in environ:
                environ['REQUEST_SCHEME'] = 'http'
            if 'HTTP_HOST' not in environ:
                server_name = environ.get('SERVER_NAME', 'localhost')
                server_port = environ.get('SERVER_PORT', '80')
                if server_port in ('80', '443'):
                    environ['HTTP_HOST'] = server_name
                else:
                    environ['HTTP_HOST'] = f"{server_name}:{server_port}"
            if 'REMOTE_ADDR' not in environ:
                environ['REMOTE_ADDR'] = '127.0.0.1'

            return original_app(environ, start_response)

        app = wrapped_app
        print(f"[OK] Successfully loaded {service_name} from {module_name}")

    except ImportError as e:
        print(f"ERROR: Could not import {module_name}: {e}")
        print("Make sure you're in the correct directory and the module exists.")
        return
    except Exception as e:
        print(f"ERROR: Could not load {module_name}: {e}")
        return

    print("=" * 60)
    print(f"Starting {service_name.upper()} service on http://{host}:{port}")
    print("Press Ctrl+C to stop the server")
    print("=" * 60)

    try:
        with make_server(host, port, app) as httpd:
            httpd.serve_forever()
    except KeyboardInterrupt:
        print(f"\n{service_name.upper()} server stopped.")
    except Exception as e:
        print(f"\nERROR: Could not start server: {e}")


if __name__ == '__main__':
    import argparse

    parser = argparse.ArgumentParser(description='Simple Slicer WSGI Development Server')
    parser.add_argument('service', nargs='?', default=DEFAULT_SERVICE,
                       help=f'Service to run (default: {DEFAULT_SERVICE})')
    parser.add_argument('--host', default='localhost', help='Host to bind to (default: localhost)')
    parser.add_argument('--port', type=int, default=8000, help='Port to bind to (default: 8000)')

    args = parser.parse_args()

    run_single_service(args.service, args.host, args.port)
