/*
Slicer Enhanced Navigation JavaScript
====================================
This JavaScript should be embedded inline in each WSGI module for production compatibility.
No external files required.
*/

// Enhanced URL navigation with loading states
function URLjump(jumpLocation, buttonElement = null) {
    // Add loading state if button provided
    if (buttonElement) {
        const originalText = buttonElement.innerHTML;
        buttonElement.innerHTML = '<span class="loading"></span> Loading...';
        buttonElement.style.pointerEvents = 'none';

        // Restore after a delay in case navigation fails
        setTimeout(() => {
            buttonElement.innerHTML = originalText;
            buttonElement.style.pointerEvents = 'auto';
        }, 5000);
    }

    location.href = jumpLocation;
}

// Enhanced navigation with visual feedback
function navigateWithFeedback(url, description) {
    console.log(`Navigating to ${description}: ${url}`);

    // Add visual feedback
    document.body.style.cursor = 'wait';

    // Navigate
    window.location.href = url;
}

// Auto-refresh functionality for dashboard pages
function setupAutoRefresh(intervalMs = 30000) {
    if (window.location.pathname.includes('dashboard')) {
        setInterval(() => {
            // Only refresh if page is visible
            if (!document.hidden) {
                window.location.reload();
            }
        }, intervalMs);
    }
}

// Enhanced module button creation
function createModuleButton(moduleName, displayName, cssClass = '') {
    return `
        <a href="${moduleName}"
           class="module-button ${cssClass}"
           onclick="URLjump('${moduleName}', this); return false;"
           title="Access ${displayName || moduleName} module">
            ${displayName || moduleName}
        </a>
    `;
}

// Create navigation section
function createNavSection(title, modules, sectionClass = '', moduleClass = '') {
    if (!modules || modules.length === 0) return '';

    let html = `
        <div class="nav-section ${sectionClass}">
            <div class="section-title">${title}</div>
            <div class="module-grid">
    `;

    modules.forEach(module => {
        const displayName = getModuleDisplayName(module);
        html += createModuleButton(module, displayName, moduleClass);
    });

    html += `
            </div>
        </div>
    `;

    return html;
}

// Get user-friendly display names for modules
function getModuleDisplayName(module) {
    const displayNames = {
        'index': 'Home',
        'dashboard': 'Dashboard',
        'reports': 'Reports',
        'login': 'Login',
        'logout': 'Logout',
        'datastore': 'Data Store',
        'debug': 'Debug Tools',
        'loader': 'Loader',
        'management': 'Management',
        'organization': 'Organization',
        'profiles': 'Profiles',
        'sites': 'Sites',
        'timezones': 'Time Zones',
        'htmlfiles': 'HTML Files',
        'deviceupload': 'Device Upload',
        'ldapsupport': 'LDAP Support',
        'videos': 'Videos',
        'certificates': 'Certificates',
        'permissions': 'Permissions',
        'investigate': 'Investigate',
        'jamf': 'JAMF',
        'address2location': 'Address to Location',
        'thirdparty': 'Third Party',
        'multimedia': 'Multimedia',
        'dataport': 'Data Port',
        'speedtest': 'Speed Test'
    };

    return displayNames[module] || module.charAt(0).toUpperCase() + module.slice(1);
}

// Build enhanced navigation HTML
function buildEnhancedNavigation(modulePermissions, environ = {}) {
    const {
        to_show_above_login = [],
        to_show_for_login = [],
        to_show_below_login = [],
        current_user = null
    } = modulePermissions;

    let html = '<div class="nav-container">';

    // Public modules section
    if (to_show_above_login.length > 0) {
        html += createNavSection(
            'Public Access',
            to_show_above_login,
            'public',
            'success'
        );
    }

    // User authentication section
    if (!current_user && to_show_for_login.length > 0) {
        html += createNavSection(
            'Authentication',
            to_show_for_login,
            'auth',
            'primary'
        );
    }

    // User-specific section
    if (current_user) {
        html += `
            <div class="user-info">
                <div class="username">Welcome, ${current_user}</div>
                <a href="login?action=logout" class="module-button danger"
                   onclick="URLjump('login?action=logout', this); return false;">
                    Logout
                </a>
            </div>
        `;

        if (to_show_below_login.length > 0) {
            html += createNavSection(
                'User Modules',
                to_show_below_login,
                'auth'
            );
        }
    }

    html += '</div>';
    return html;
}

// Initialize enhanced navigation when DOM is ready
function initializeEnhancedNavigation() {
    // Add keyboard navigation
    document.addEventListener('keydown', function(e) {
        // Alt + H for home
        if (e.altKey && e.key === 'h') {
            e.preventDefault();
            URLjump('index');
        }
        // Alt + D for dashboard
        if (e.altKey && e.key === 'd') {
            e.preventDefault();
            URLjump('dashboard');
        }
        // Alt + R for reports
        if (e.altKey && e.key === 'r') {
            e.preventDefault();
            URLjump('reports');
        }
    });

    // Setup auto-refresh for dashboard
    setupAutoRefresh();

    // Add accessibility improvements
    const buttons = document.querySelectorAll('.module-button');
    buttons.forEach((button, index) => {
        button.setAttribute('tabindex', index + 1);

        // Add keyboard support
        button.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                button.click();
            }
        });
    });

    console.log('Slicer Enhanced Navigation initialized');
}

// Initialize when page loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeEnhancedNavigation);
} else {
    initializeEnhancedNavigation();
}
