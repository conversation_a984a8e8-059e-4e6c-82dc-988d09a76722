# A index for slicer page services

service = "index"
version = service + '.0.5'

_ = """
This file gets loaded to:
/var/www/html/index.py

using:
sudo vi /var/www/html/index.py

or for development gets loaded to:
sudo vi /var/www/html/indexd.py


It also requires:

sudo vi /etc/httpd/conf.d/python-index.conf
----- start copy -----
WSGIScriptAlias /index /var/www/html/index.py
----- end copy -----

It also requires:

sudo vi /etc/httpd/conf/httpd.conf
----- start copy / paste to end of file -----
<Directory "/">
DirectoryIndex index
</Directory>
----- end copy -----

sudo chown apache:apache /var/www/html/index.py

sudo systemctl restart httpd

sudo systemctl status httpd

Slicer03:
sudo cat /var/log/httpd/error_log
sudo cat /var/log/httpd/access_log
sudo cat /var/log/httpd/ssl_access_log
sudo cat /var/log/httpd/ssl_error_log
sudo cat /var/log/httpd/ssl_request_log

test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import index; print(index.make_body({'REQUEST_METHOD':'GET', 'QUERY_STRING':'','REMOTE_ADDR':'0.0.0.0'}))"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net


"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest slicer_wsgi_index

"""

import copy
import traceback
import json
import os
import shlex
import subprocess
import sys
import time
import unittest

startup_exceptions = ''
s_loader_module_save_path = '/dev/shm/loader_modules.txt'

service_config = {}
path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)

try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)
except:
    pass

try:
    # python 2 and some early python 3
    from cgi import parse_qs
except:
    pass

try:
    # later python 3
    from urllib.parse import parse_qs
except:
    pass

try:
    import datastore
except:
    pass
try:
    import login
except:
    pass
try:
    import permissions
except:
    pass
try:
    import rings
except:
    pass
try:
    import scan
except:
    pass
try:
    import tasks
except:
    pass
try:
    import upload
except:
    pass
try:
    import htmlfiles
except:
    pass
try:
    import certificates
except:
    pass
try:
    import watchdog
except:
    pass
try:
    import dashboard
except:
    pass
try:
    import devicecommand
except:
    pass

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass

# ====================================
def make_not_allow_list_from_datastore(data_store_content):
    # ====================================
    list_of_services_to_not_allow = []
    for key in data_store_content.keys():
        if 'service_loader_' in key:
            the_service = key.replace('service_loader_','').split('_')[0]
            the_key = 'service_loader_' + the_service + '_allow_runner'
            if the_key in data_store_content:
                the_value = data_store_content[the_key]
                if the_value == 'no':
                    list_of_services_to_not_allow.append(the_service)
    return list_of_services_to_not_allow

# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ----------------------------
def do_one_command(command):
    # ----------------------------
    command_splits = shlex.split(command)
    # command_splits = command.split(" ")
    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()
    return (mem_string, fails)


# ====================================
def disk_report():
    # ====================================
    report = ""
    pass_string, fails = do_one_command('df -k')

    for line in pass_string.split('\n'):
        if not 'tmpfs' in line:
            splits = line.split()
            try:
                report += splits[0] + ' ' + splits[4] + '\n'
            except:
                pass

    pass_string, fails = do_one_command('df -i')

    for line in pass_string.split('\n'):
        if not 'tmpfs' in line:
            splits = line.split()
            try:
                report += splits[0] + ' ' + splits[4] + '\n'
            except:
                pass

    return report


# ====================================
def make_body(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    if environ['REQUEST_METHOD'] == 'GET':
        return make_body_GET(environ)

    body = ''

    try:
        if permissions.permission_prefix_allowed(environ, 'development_'):
            try:
                if environ['REQUEST_METHOD'] == 'POST':
                    return make_body_POST(environ)
            except:
                body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
        else:
            body = ""
            body += "<br><br><br><br><br>"
            body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"
    except:
        body += '<B>Exception on permissions call!</B>'

    return body, other


# ====================================
def make_body_POST(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    try:
        request_body_size = int(environ.get('CONTENT_LENGTH', 0))
    except (ValueError):
        request_body_size = 0
    request_body = environ['wsgi.input'].read(request_body_size)

    d = parse_qs(request_body.decode('utf-8'))

    if 'process_start_stop' in d:
        the_process = d['process_start_stop'][0]

        if 'process_start_stop_value' in d:
            the_value = d['process_start_stop_value'][0]

            the_request_file = '/dev/shm/' + 'service_start_stop_request_' + str(time.time()) + '.txt'

            try:
                open(the_request_file, 'w').write(the_process + ' ' + the_value)
            except:
                pass

            # hang out here a little bit, until it is processed, but bail after that
            try_to_wait = True
            time_start = time.time()
            while try_to_wait:
                if not os.path.isfile(the_request_file):
                    try_to_wait = False
                else:
                    if time.time() - time_start > 1.0:
                        try_to_wait = False
                    else:
                        time.sleep(0.1)

    #            return ('see: ' + the_process + ' ' + the_value + ' -> ' + the_request_file)

    # then return what GET would have done
    return make_body_GET(environ)

# ====================================
def make_body_GET_content(module_permissions, environ={}):
    # ====================================
    to_show_above_login = module_permissions['to_show_above_login']
    to_show_for_login = module_permissions['to_show_for_login']
    to_show_below_login = module_permissions['to_show_below_login']
    current_user = module_permissions['current_user']

    site_title = 'Slicer 2.0'
    if 'site_title' in service_config:
        site_title = service_config['site_title']

    site_build = ''
    if 'build' in service_config:
        site_build = service_config['build']

    body = ''
    try:
        body += '<center>'

        body += '<B>' + site_title + '</B>'
        if site_build:
            body += ' -> build: ' + site_build
        body += '<br><br>'
        body += '<br><br>'
        body += '</center>'

        body += '<center>'
        body += '<table border="1" cellpadding="5">'

        if not datastore.trust():
            body += '<B>Not Trusted</B>'
            body += '<a href="datastore?action=begintrust">datastore begin trust</a>'

        try:
            # do the "public" list at the top
            for module in to_show_above_login:
                body += '<tr>'
                body += '<td>'
                body += '<a href="' + module + '">' + module + '</a>'
                body += '</td>'
                body += '</tr>'

            if current_user:
                body += '<tr>'
                body += '<td>'
                body += '</td>'
                body += '</tr>'
                body += '<tr>'
                body += '<td title="Log out the current user">'
                body += '<a href="login?action=logout">logout</a>'
                body += '</td>'
                body += '<td>'
                body += current_user
                body += '</td>'
                body += '</tr>'
                body += '<tr>'
                body += '<td>'
                body += '\\/  \\/  \\/  \\/  \\/'
                body += '</td>'
                body += '</tr>'

                # Show items the current login user can see
                for module in to_show_below_login:
                    body += '<tr>'
                    body += '<td>'
                    body += '<a href="' + module + '">' + module + '</a>'
                    body += '</td>'

                    if module == 'datastore':
                        if datastore.trust():
                            body += 'Trusted'
                        else:
                            body += '<B>Not Trusted</B>'
                            body += '<td>'
                            body += '<a href="datastore?action=begintrust">datastore begin trust</a>'
                            body += '</td>'
                    body += '</tr>'
            else:
                if not to_show_above_login:
                    # go ahead, and just go to the login page
                    if to_show_for_login:
                        url_to_use = make_home_url_from_environ(environ)
                        redirect = url_to_use + '/login'
                        other = {'status': '303 See Other', 'response_header': [('Location', redirect)]}
                else:
                    # if login is in the public list, then show it here
                    for module in to_show_for_login:
                        body += '<tr>'
                        body += '<td>'
                        body += '</td>'
                        body += '</tr>'
                        body += '<tr>'
                        body += '<td>'
                        body += '<a href="' + module + '">' + module + '</a>'
                        body += '</td>'
                        body += '</tr>'
        except:
            body += '<tr>'
            body += '<td>'
            body += '<B>Exception on permissions call!</B>'
            body += str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
            body += '</td>'
            body += '</tr>'

        body += '</table>'
        body += '</center>'

    except Exception as e:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body

# ====================================
def make_body_GET(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}


    if True:
        user = login.get_current_user(environ)
        data_store_content = datastore.all_datastore()
        permissions_cache = permissions.make_permissions_cache_from_datastore(data_store_content)
        not_allow_list = make_not_allow_list_from_datastore(data_store_content)
        modules_found_file_content = ''
        try:
            modules_found_file_content = open(s_loader_module_save_path,'r').read()
        except:
            pass
        module_permissions = permissions.make_module_permissions_for_user_from_permissions_cache(user,permissions_cache,not_allow_list,modules_found_file_content)
    else:
        module_permissions = permissions.get_module_permissions_for_environ(environ)

    body = make_body_GET_content(module_permissions, environ)

    return body, other


# ====================================
def make_body_parts_for_service_controls(the_service, restart_only=False):
    # ====================================
    body = ''

    for next_Value in ['start', 'stop', 'restart']:
        color = "(255, 0, 0, 0.0)"

        body += '<td title="' + next_Value + '" style="background-color:rgba' + color + '">'

        make_it = True
        if restart_only:
            if next_Value != 'restart':
                make_it = False

        if make_it:
            body += '<form method="post" action="">'
            body += '<select name="process_start_stop" id="process_start_stop" hidden>'
            body += '<option value="' + the_service + '" selected>' + the_service + '</option>'
            body += '</select>'

            body += '<select name="process_start_stop_value" id="process_start_stop_value" hidden>'
            body += '<option value="' + next_Value + '" selected>' + next_Value + '</option>'
            body += '</select>'
            body += '<center>'
            body += '<input type="submit" value="' + next_Value + '">'
            body += '</center>'
            body += '</form>'
        body += '</td>'

    return body


# Main is the loop for the "template-runner" that the service starts
# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1

        time.sleep(2)


# ====================================
def application(environ, start_response):
    # ====================================

    value_test = 0
    if 'HTTP_COOKIE' in environ:
        value_test = 1

    status = '200 OK'
    response_header = [('Content-type', 'text/html')]

    html = ''
    try:
        body, other = make_body(environ)
        status = other['status']
        head = ''
        if 'head' in other:
            head = other['head']
        response_header = other['response_header']
        if other['add_wrapper']:
            html += '<html>\n'
            if head:
                html += '<head>\n'
                html += head
                html += '</head>\n'
            html += '<body>\n'
        html += body
        if other['add_wrapper']:
            html += '</body>\n'
            html += '</html>\n'

    #        response_header.append(set_cookie_header('name_test', str(value_test)))
    #        response_header.append(set_cookie_header('name_test2', str(10+value_test)))

    except:
        html += '<html>\n' \
                '<body>\n'
        html += str(sys.version_info)
        html += str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
        html += '</body>\n' \
                '</html>\n'

    try:
        html = organization.wrap_page_with_session(environ, html)
        start_response(status, response_header)
    except:
        # still on slicer01
        # allow non wrapped response
        start_response(status, response_header)

    return [html.encode()]


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_template(self):
        """
        (fill in here)
        """
        self.assertEqual(True, True)

# End of source file
